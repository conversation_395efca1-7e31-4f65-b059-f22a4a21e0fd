package com.dexpo.module.member.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import com.dexpo.framework.common.exception.enums.MemberServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.excel.core.util.ExcelUtils;
import com.dexpo.framework.mybatis.core.query.QueryWrapperX;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.integration.api.flie.FileApi;
import com.dexpo.module.integration.utils.FileUtils;
import com.dexpo.module.member.api.dto.EnterpriseInfoDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyRecordDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyRegistrationDTO;
import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.api.vo.media.MediaProxyRecordVO;
import com.dexpo.module.member.app.converter.EnterpriseInfoDtoConverter;
import com.dexpo.module.member.app.converter.MediaProxyRecordDtoConverter;
import com.dexpo.module.member.app.converter.MediaProxyRecordVoConverter;
import com.dexpo.module.member.app.converter.MediaProxyRegistrationDtoConverter;
import com.dexpo.module.member.app.service.MediaProxyCheckDateAppService;
import com.dexpo.module.member.app.service.MediaProxyRegistrationAppService;
import com.dexpo.module.member.app.service.MediaProxySaveDateAppService;
import com.dexpo.module.member.domain.constant.MediaProxyConstant;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.member.MemberMediaProxyRecord;
import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import com.dexpo.module.member.domain.repository.EnterpriseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberMediaProxyRecordRepository;
import com.dexpo.module.member.domain.repository.MemberRecipientInfoRepository;
import com.dexpo.module.member.domain.utils.ZIPUtils;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberMediaProxyRecordDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberMediaProxyRecordMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class MediaProxyRegistrationAppServiceImpl implements MediaProxyRegistrationAppService {

    @Value("${MediaProxyRegistration.tempFilePath}")
    private String tempFilePath;

//    @Resource
//    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Resource
    private EnterpriseInfoRepository enterpriseInfoRepository;

    @Resource
    private MediaProxySaveDateAppService mediaProxySaveDateAppService;

    @Resource
    private MediaProxyCheckDateAppService mediaProxyCheckDateService;

    @Resource
    private MemberMediaProxyRecordMapper memberMediaProxyRecordMapper;

    @Resource
    private MemberMediaProxyRecordRepository memberMediaProxyRecordRepository;

//    @Resource
//    private MemberRecipientInfoMapper memberRecipientInfoMapper;

    @Resource
    private MemberRecipientInfoRepository memberRecipientInfoRepository;

    @Resource
    private FileApi fileApi;

    @Override
    public void importMediaList(Long memberMediaProxyRecordId, Long exhibitionId, byte[] content) {
        MediaProxyActionDTO mediaProxyActionDTO = new MediaProxyActionDTO();
        mediaProxyActionDTO.setExhibitionId(exhibitionId);
        mediaProxyActionDTO.setMemberMediaProxyRecordId(memberMediaProxyRecordId);
        // 1. 解析zip压缩包文件,获取文件目录和对应文件信息
        Map<String, String> fileNameAndFileMap = ZIPUtils.unZip(tempFilePath, content, MediaProxyConstant.EXCEL_KEY);
        // 2. 解析execl获取导入的媒体信息
        List<MediaProxyRegistrationDTO> list = getMediaProxyRegistrationDTOS(fileNameAndFileMap);

        mediaProxyActionDTO.setMediaProxyRegistrationList(list);

        // 3. 校验execl表信息（包括校验其中附件信息是否与文件目录和对应文件信息一致）
        mediaProxyCheckDateService.checkExcelToType(mediaProxyActionDTO);

        // 检查用户基本信息是否已注册
        mediaProxyCheckDateService.checkExcelToInfo(mediaProxyActionDTO);

        // 检查姓名和身份证号是否匹配，只检查 中国居民身份证 类型的信息
        mediaProxyCheckDateService.checkExcelToVerification(mediaProxyActionDTO);

        // 4. 如有错误，记录错误到数据库，将异步导入信息变更为导入失败
        if (mediaProxyActionDTO.isHasError()) {
            saveCheckErrorInfo(mediaProxyActionDTO);
            log.info("mediaProxyActionDTO -> {}", mediaProxyActionDTO);
            return;
        }

        if (CollUtil.isEmpty(mediaProxyActionDTO.getMediaProxyRegistrationList())) {
            return;
        }

        // 5. 如无错误，上传文件到oss；
        Map<String, String> fileMap = uploadFileToOss(fileNameAndFileMap);

        // 6. 注册未注册的媒体-企业
        batchAddNewEnterprises(mediaProxyActionDTO);

        // 7. 注册未注册的媒体用户
        mediaProxySaveDateAppService.batchAddNewMediaUser(mediaProxyActionDTO);

        // 媒体用户拓展信息数据对象
        mediaProxySaveDateAppService.batchAddNewMemberReporterInfo(mediaProxyActionDTO, fileMap);

        //  用户参展记录数据对象
        mediaProxySaveDateAppService.batchAddNewMemberParticipateRecord(mediaProxyActionDTO);

        // 保存用户收证信息数据对象
        saveOrUpdateMemberRecipientInfoDO(mediaProxyActionDTO);

        // 同步数据到数仓
        mediaProxySaveDateAppService.batchAddNewMemberDataWarehouse(mediaProxyActionDTO);

    }

    private void saveOrUpdateMemberRecipientInfoDO(MediaProxyActionDTO mediaProxyActionDTO) {

        List<MemberBaseInfoDTO> list = mediaProxyActionDTO.getMemberBaseInfoDOList();
        List<MemberRecipientInfo> memberRecipientInfoDOList = new ArrayList<>();
        for (MemberBaseInfoDTO memberBaseInfo : list) {
            MediaProxyRegistrationDTO one = mediaProxyActionDTO.getMediaProxyRegistrationMapOne(
                    memberBaseInfo.getMemberMobile(),
                    memberBaseInfo.getMemberEmail());
            MemberRecipientInfo memberRecipientInfo = MediaProxyRegistrationDtoConverter.INSTANCE.toMemberRecipientInfo(one);
//            MemberRecipientInfo memberRecipientInfo = MemberRecipientInfoConvert
//                    .INSTANCE.mediaProxyRegi2MemberRecipientInfoDO(one);
            memberRecipientInfo.setMemberId(memberBaseInfo.getId());
            memberRecipientInfoDOList.add(memberRecipientInfo);
        }
        memberRecipientInfoRepository.addBatch(memberRecipientInfoDOList);
    }



    public void batchAddNewEnterprises(MediaProxyActionDTO mediaProxyActionDTO) {
        List<EnterpriseInfoDTO> newEnterpriseList = mediaProxyActionDTO.getNewEnterprise();
        Map<String, EnterpriseInfoDTO> collect = newEnterpriseList.stream()
                .collect(Collectors.toMap(EnterpriseInfoDTO::getEnterpriseName, e -> e,
                        (v1, v2) -> v1));
        List<EnterpriseInfoDTO> values = new ArrayList<>(collect.values());
        if (CollUtil.isNotEmpty(values)) {
            List<EnterpriseInfo> enterpriseInfos = EnterpriseInfoDtoConverter.INSTANCE.toModelList(values);
            enterpriseInfoRepository.addBatch(enterpriseInfos);
            Map<String, EnterpriseInfoDTO> map = values.stream()
                    .collect(Collectors.toMap(EnterpriseInfoDTO::getEnterpriseName, e -> e));
            for (MediaProxyRegistrationDTO dto : mediaProxyActionDTO.getMediaProxyRegistrationList()) {
                if (ObjectUtil.isNotEmpty(dto.getEnterpriseId())) {
                    dto.setEnterpriseId(map.get(dto.getEnterpriseName()).getId());
                }
            }
        }

    }

    public List<MediaProxyRegistrationDTO> getMediaProxyRegistrationDTOS(Map<String, String> fileNameAndFileMap) {
        List<MediaProxyRegistrationDTO> list;
        try {
            FileInputStream excelFileStream = new FileInputStream(fileNameAndFileMap.get(MediaProxyConstant.EXCEL_KEY));
            list = ExcelUtils.read(excelFileStream, MediaProxyRegistrationDTO.class, 3, 0);
        } catch (IOException e) {
            log.info(MemberServiceErrorCodeEnum.MEMBER_EXCEL_HAS_ERROR.getMsg(), e);
            throw MemberServiceErrorCodeEnum.MEMBER_EXCEL_HAS_ERROR.getServiceException();
        }
        return list;
    }

    /**
     * 将错误信息保存到数据库
     *
     * @param mediaProxyActionDTO
     */
    public void saveCheckErrorInfo(MediaProxyActionDTO mediaProxyActionDTO) {
        MemberMediaProxyRecord memberMediaProxyRecord =
                memberMediaProxyRecordRepository.queryById(mediaProxyActionDTO.getMemberMediaProxyRecordId());
        memberMediaProxyRecord.setErrorInfo(mediaProxyActionDTO.getErrorInfo().toString());
        memberMediaProxyRecordRepository.addOrModify(memberMediaProxyRecord);
    }


    private Map<String, String> uploadFileToOss(Map<String, String> fileNameAndFileMap) {
        Map<String, String> fileMap = new HashMap<>();
        for (Map.Entry<String, String> entry : fileNameAndFileMap.entrySet()) {
            String fileName = entry.getKey();
            String filePath = entry.getValue();
            try {
                LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
                String path = FileUtils.getFilePath(loginUser.getMemberCode());
                CommonResult<String> stringCommonResult = fileApi.uploadFileByBytes(fileName, path, Files.readAllBytes(new File(filePath).toPath()));
                if (stringCommonResult.isSuccess()) {
                    fileMap.put(fileName, stringCommonResult.getData());
                }
            } catch (IOException e) {
                throw ErrorCodeEnums.BFF_USER_FILE_UPLOAD_FAIL.getServiceException();
            }
        }
        return fileMap;
    }

    /**
     * 保存批量代注册
     */
    @Override
    public Long saveMemberMediaProxyRecord(MediaProxyRecordDTO mediaProxyRecordDTO) {

        MemberMediaProxyRecord memberMediaProxyRecord = MediaProxyRecordDtoConverter.INSTANCE.toModel(mediaProxyRecordDTO);
        memberMediaProxyRecordRepository.addOrModify(memberMediaProxyRecord);
        return memberMediaProxyRecord.getId();
    }

    @Override
    public PageResult<MediaProxyRecordVO> queryMemberMediaProxyRecordPage(MediaProxyRecordDTO mediaProxyRecordDTO) {
        QueryWrapperX<MemberMediaProxyRecordDO> wrapperX = new QueryWrapperX<>();
        PageResult<MemberMediaProxyRecordDO> pageResult = memberMediaProxyRecordMapper.selectPage(mediaProxyRecordDTO, wrapperX);
        PageResult<MediaProxyRecordVO> pageVo = new PageResult<>();

        pageVo.setList(MediaProxyRecordVoConverter.INSTANCE.toVoList(pageResult.getList()));
        pageVo.setTotal(pageResult.getTotal());
        return pageVo;
    }

}
