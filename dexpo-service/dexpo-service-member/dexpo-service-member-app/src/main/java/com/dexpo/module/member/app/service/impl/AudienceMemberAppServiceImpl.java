package com.dexpo.module.member.app.service.impl;

import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.member.api.dto.audience.AudiencePageQueryDTO;
import com.dexpo.module.member.api.vo.audience.AudiencePageListVO;
import com.dexpo.module.member.app.converter.AudiencePageListVoConverter;
import com.dexpo.module.member.app.converter.AudiencePageQueryDtoConverter;
import com.dexpo.module.member.app.external.exhibition.ExhibitionExternalService;
import com.dexpo.module.member.app.service.AudienceMemberAppService;
import com.dexpo.module.member.domain.model.audience.AudiencePageList;
import com.dexpo.module.member.domain.model.audience.AudiencePageListQuery;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.service.AudienceMemberDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class AudienceMemberAppServiceImpl implements AudienceMemberAppService {

    private final ExhibitionExternalService exhibitionExternalService;

    private final MemberBaseInfoRepository memberBaseInfoRepository;

    @Override
    public PageResult<AudiencePageListVO> getAudiencePage(AudiencePageQueryDTO queryDTO) {
        AudiencePageListQuery queryDO = AudiencePageQueryDtoConverter.INSTANCE.toAudiencePageListQuery(queryDTO);
        // 不管用户是否选择了展会，都需要一个默认的条件，即当前用户登录对应的展会权限。
        ExhibitionQueryDTO exhibitionQueryDTO = new ExhibitionQueryDTO(queryDTO.getExhibitionTagCodes(),
                queryDTO.getExhibitionSessionKeys(), true);
        Map<Long, ExhibitionVO> exhibitionMap = exhibitionExternalService.getExhibitionMap(exhibitionQueryDTO);
        if (CollectionUtils.isEmpty(exhibitionMap)) {
            return PageResult.empty();
        }
        queryDO.setExhibitionIds(exhibitionMap.keySet().stream().toList());
        PageResult<AudiencePageList> pageResult = memberBaseInfoRepository.getAudiencePage(queryDO, ValueSetUserTypeEnum.AUDIENCE.getOptionCode());
        List<AudiencePageListVO> vos = AudiencePageListVoConverter.INSTANCE.toAudiencePageListVOS(pageResult.getList());
        // 回填展会信息
        vos.forEach(v -> {
            v.setExhibitionNameCn(exhibitionMap.get(v.getExhibitionId()).getExhibitionNameCn());
            v.setExhibitionNameEn(exhibitionMap.get(v.getExhibitionId()).getExhibitionNameEn());
            v.setExhibitionCode(exhibitionMap.get(v.getExhibitionId()).getExhibitionCode());
            v.setExhibitionYear(exhibitionMap.get(v.getExhibitionId()).getExhibitionYear());
            v.setExhibitionSession(exhibitionMap.get(v.getExhibitionId()).getExhibitionSession());
        });
        return new PageResult<>(vos, pageResult.getTotal());

    }

    @Override
    public Boolean active(List<Long> ids) {
        audienceMemberDomainService.active(ids);
        return true;
    }
}
