package com.dexpo.module.member.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.dexpo.module.base.enums.MediaCategoryEnums;
import com.dexpo.module.base.enums.MediaPositionEnums;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseSyncMessage;
import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;
import com.dexpo.module.member.api.dto.media.MediaProxyRegistrationDTO;
import com.dexpo.module.member.api.dto.member.MemberBaseInfoDTO;
import com.dexpo.module.member.app.converter.MediaProxyRegistrationDtoConverter;
import com.dexpo.module.member.app.converter.MemberBaseInfoDtoConverter;
import com.dexpo.module.member.app.mq.DataWarehouseChannelProducer;
import com.dexpo.module.member.app.service.MediaProxySaveDateAppService;
import com.dexpo.module.member.domain.constant.MediaProxyConstant;
import com.dexpo.module.member.domain.enums.*;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;
import com.dexpo.module.member.domain.model.member.MemberReporterInfo;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import com.dexpo.module.member.domain.repository.MemberReporterInfoRepository;
import com.dexpo.module.member.enums.RegisterStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Validated
@Slf4j
public class MediaProxySaveDateAppServiceImpl implements MediaProxySaveDateAppService {

    @Resource
    private MemberBaseInfoMapper memberBaseInfoMapper;

    @Resource
    private MemberBaseInfoRepository memberBaseInfoRepository;

    @Resource
    private DataWarehouseChannelProducer dataWarehouseChannelProducer;

    @Resource
    private MemberReporterInfoMapper reporterInfoMapper;

    @Resource
    private MemberReporterInfoRepository reporterInfoRepository;

    @Resource
    private MemberParticipateRecordMapper memberParticipateRecordMapper;

    @Resource
    private MemberParticipateRecordRepository memberParticipateRecordRepository;

    @Override
    public void batchAddNewMediaUser(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MemberBaseInfo> memberBaseInfoDOList = MediaProxyRegistrationDtoConverter.INSTANCE
                .toMemberBaseInfoList(mediaProxyActionDTO.getMediaProxyRegistrationList());
        if (CollUtil.isEmpty(memberBaseInfoDOList)) {
            return;
        }
        for (MemberBaseInfo e : memberBaseInfoDOList) {
            e.setMemberCode(UUID.fastUUID().toString().replace("-", ""));
        }
        memberBaseInfoRepository.addBatch(memberBaseInfoDOList);
        mediaProxyActionDTO.setMemberBaseInfoDOList(MemberBaseInfoDtoConverter.INSTANCE.toDtoList(memberBaseInfoDOList));
    }

    @Override
    public void batchAddNewMemberReporterInfo(MediaProxyActionDTO mediaProxyActionDTO, Map<String, String> fileMap) {
        List<MemberReporterInfo> reporterInfoDOList = new ArrayList<>();
        List<MemberBaseInfoDTO> memberBaseInfoDOList = mediaProxyActionDTO.getMemberBaseInfoDOList();

        for (MemberBaseInfoDTO baseInfoDO : memberBaseInfoDOList) {
            MemberReporterInfo memberReporterInfo = new MemberReporterInfo();
            memberReporterInfo.setMemberId(baseInfoDO.getId());

            //整理信息
            MediaProxyRegistrationDTO mediaProxyRegistrationMapOne = mediaProxyActionDTO
                    .getMediaProxyRegistrationMapOne(baseInfoDO.getMemberMobile(), baseInfoDO.getMemberEmail());
            BeanUtil.copyProperties(mediaProxyRegistrationMapOne, memberReporterInfo);
            BeanUtil.copyProperties(baseInfoDO, memberReporterInfo);

            MediaCategoryEnums mediaCategoryEnums = MediaPositionEnums
                    .getByCode(memberReporterInfo.getMediaPositionCode()).getMediaCategoryEnums();
            memberReporterInfo.setMediaPositionCategoryCode(mediaCategoryEnums.getCode());
            memberReporterInfo.setMediaPositionCategoryNameEn(mediaCategoryEnums.getDescriptionEN());
            memberReporterInfo.setMediaPositionCategoryNameCn(mediaCategoryEnums.getDescriptionCN());
            
            memberReporterInfo.setMemberId(baseInfoDO.getId());
            memberReporterInfo.setExhibitionId(mediaProxyActionDTO.getExhibitionId());
            memberReporterInfo.setEnterpriseId(mediaProxyRegistrationMapOne.getEnterpriseId());

            // 保存头像
            savePhoto(fileMap, baseInfoDO, memberReporterInfo);

            reporterInfoDOList.add(memberReporterInfo);
        }

        // 批量新增
        reporterInfoRepository.addBatch(reporterInfoDOList);
    }

    private void savePhoto(Map<String, String> fileMap, MemberBaseInfoDTO baseInfoDO, MemberReporterInfo memberReporterInfo) {
        StringBuilder attachmentOtherDescribe = new StringBuilder();
        if (CollUtil.isEmpty(fileMap)) {
            return;
        }

        for (Map.Entry<String, String> entry : fileMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key.contains(baseInfoDO.getIdNumber())) {
                if (key.contains(MediaProxyConstant.TYPE_ID_PHOTO)) {
                    memberReporterInfo.setAttachmentHeadPhoto(value);
                }
                if (key.contains(MediaProxyConstant.TYPE_ATTACHMENTS)) {
                    attachmentOtherDescribe.append(value).append(",");
                }
            }
        }
        if (!attachmentOtherDescribe.isEmpty()) {
            memberReporterInfo.setAttachmentOtherDescribe(
                    attachmentOtherDescribe.deleteCharAt(attachmentOtherDescribe.length() - 1).toString());
        }
    }

    @Override
    public void batchAddNewMemberParticipateRecord(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MemberParticipateRecord> memberParticipateRecordDOList = new ArrayList<>();
        List<MemberBaseInfoDTO> memberBaseInfoDOList = mediaProxyActionDTO.getMemberBaseInfoDOList();
        for (MemberBaseInfoDTO baseInfoDO : memberBaseInfoDOList) {
            MemberParticipateRecord recordDO = new MemberParticipateRecord();
            recordDO.setMemberId(baseInfoDO.getId());
            recordDO.setExhibitionId(mediaProxyActionDTO.getExhibitionId());
            recordDO.setMemberType(ActionUserTypeEnum.MEDIA_USER.getCode());
            recordDO.setRegisterTime(LocalDateTime.now());
            recordDO.setRegisterSource(RegisterSourceEnum.NORMAL.getCode());
            recordDO.setRegisterMethod(RegisterMethodEnum.AGENT_REGISTER.getCode());
            recordDO.setRegisterLanguage(RegisterLanguageEnum.CHINESE.getCode());

            recordDO.setRegisterStatus(RegisterStatusEnum.APPROVED.getCode());
            recordDO.setRegisterSystem(RegisterSystemEnum.CENTRAL_PLATFORM.getCode());
            recordDO.setCreateUser(baseInfoDO.getId());
            recordDO.setCreateTime(LocalDateTime.now());
            recordDO.setIsActive(Boolean.FALSE);
            recordDO.setDelFlg(Boolean.FALSE);
            memberParticipateRecordDOList.add(recordDO);
        }
        memberParticipateRecordRepository.addBatch(memberParticipateRecordDOList);
    }

    @Override
    public void batchAddNewMemberDataWarehouse(MediaProxyActionDTO mediaProxyActionDTO) {
        List<MemberBaseInfo> memberBaseInfoDOList = mediaProxyActionDTO.getMemberBaseInfoDOList();
        for (MemberBaseInfo baseInfoDO : memberBaseInfoDOList) {
            DataWarehouseSyncMessage msg = new DataWarehouseSyncMessage();
            BeanUtil.copyProperties(baseInfoDO, msg);
            MediaProxyRegistrationDTO mediaProxyRegistrationMapOne = mediaProxyActionDTO
                    .getMediaProxyRegistrationMapOne(baseInfoDO.getMemberMobile(), baseInfoDO.getMemberEmail());
            BeanUtil.copyProperties(mediaProxyRegistrationMapOne, msg);
            dataWarehouseChannelProducer.dataWarehouseChannel(msg);
        }
    }
}
