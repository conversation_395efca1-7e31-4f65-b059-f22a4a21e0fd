package com.dexpo.module.member.app.converter;

import com.dexpo.module.member.api.dto.media.MediaProxyRegistrationDTO;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MediaProxyRegistrationDtoConverter {


    MediaProxyRegistrationDtoConverter INSTANCE = Mappers.getMapper(MediaProxyRegistrationDtoConverter.class);


    MemberRecipientInfo toMemberRecipientInfo(MediaProxyRegistrationDTO data);

    List<MemberBaseInfo> toMemberBaseInfoList(List<MediaProxyRegistrationDTO> dataList);
}
