package com.dexpo.module.member.app.service;


import com.dexpo.module.member.api.dto.media.MediaProxyActionDTO;

import java.util.Map;

public interface MediaProxySaveDateAppService {

    void batchAddNewMediaUser(MediaProxyActionDTO mediaProxyActionDTO);

    void batchAddNewMemberReporterInfo(MediaProxyActionDTO mediaProxyActionDTO, Map<String, String> fileMap);

    void batchAddNewMemberParticipateRecord(MediaProxyActionDTO mediaProxyActionDTO);

    void batchAddNewMemberDataWarehouse(MediaProxyActionDTO mediaProxyActionDTO);
}
