package com.dexpo.module.member.app.service;


import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.member.api.dto.media.MediaProxyRecordDTO;
import com.dexpo.module.member.api.vo.media.MediaProxyRecordVO;

/**
 * 媒体用户接口
 */
public interface MediaProxyRegistrationAppService {

    void importMediaList(Long memberMediaProxyRecordId, Long exhibitionId, byte[] content);

    Long saveMemberMediaProxyRecord(MediaProxyRecordDTO mediaProxyRecordDTO);

    PageResult<MediaProxyRecordVO> queryMemberMediaProxyRecordPage(MediaProxyRecordDTO mediaProxyRecordDTO);
}
