<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-service-member</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dexpo-service-member-app</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-member-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-member-infrastructure</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-member-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-exhibition-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dexpo</groupId>
            <artifactId>dexpo-service-integration-api</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>