package com.dexpo.module.member.domain.model.member;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 批量媒体代注册记录表
 */
@Data
public class MemberMediaProxyRecord {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 上传人姓名
     */
    private String uploaderName;

    /**
     * 上传人ID
     */
    private Long uploaderId;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 错误记录
     */
    private String errorInfo;

}
