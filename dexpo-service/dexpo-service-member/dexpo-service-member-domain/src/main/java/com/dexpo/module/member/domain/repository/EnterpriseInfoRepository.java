package com.dexpo.module.member.domain.repository;


import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfoQuery;

import java.util.List;

public interface EnterpriseInfoRepository {
    List<EnterpriseInfo> getEnterpriseInfoList(EnterpriseInfoQuery infoQuery);

    EnterpriseInfo getById(Long enterpriseId);

    EnterpriseInfo queryMediaEnterpriseByNameAndLocationCode(String enterpriseName, String enterpriseLocationCode);

    void addOrModify(EnterpriseInfo enterpriseInfo);

    List<EnterpriseInfo> queryAll();

    void addBatch(List<EnterpriseInfo> enterpriseInfos);
}
