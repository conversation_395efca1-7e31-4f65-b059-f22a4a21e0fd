package com.dexpo.module.member.domain.repository;

import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;

import java.util.List;
import java.util.Set;

public interface MemberParticipateRecordRepository {
    void active(List<Long> ids);

    MemberParticipateRecord queryByUserIdAndExhibitionId(Long memberId, Long exhibitionId);

    void modifyById(MemberParticipateRecord memberParticipateRecord);

    void add(MemberParticipateRecord memberParticipateRecord);

    void modifyBatch(List<MemberParticipateRecord> memberParticipateRecords);

    List<MemberParticipateRecord> queryByMemberIdAndExhibitionId(Set<Long> memberIdSet, Long exhibitionId);

    void addBatch(List<MemberParticipateRecord> memberParticipateRecords);
}
