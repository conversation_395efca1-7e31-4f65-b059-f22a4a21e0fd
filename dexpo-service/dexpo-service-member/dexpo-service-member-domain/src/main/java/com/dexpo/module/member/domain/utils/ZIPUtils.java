package com.dexpo.module.member.domain.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
public class ZIPUtils {


    public static Map<String, String> unZip(String contextPath, byte[] content, String excelKey) {
        if (content == null || content.length == 0) {
            log.info("上传失败，文件为空");
            return new HashMap<>();
        }
        
        Map<String, String> res = new HashMap<>();
        try (InputStream inputStream = new ByteArrayInputStream(content);
             ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {

            ZipEntry zipEntry;

            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                String filePathAndName = contextPath + zipEntry.getName();
                if (zipEntry.isDirectory()) {
                    new File(filePathAndName).mkdirs();
                    continue;
                }
                // 获取媒体批量注册信息的excel
                if (zipEntry.getName().contains(".xlsx") || zipEntry.getName().contains(".xls")) {
                    res.put(excelKey, filePathAndName);
                }
                mkdirAndWriteFile(filePathAndName, res, zipEntry, zipInputStream);
            }
            log.info("文件解压成功");
        } catch (IOException e) {
            log.info("文件解压失败：{}", e.getMessage());
        }
        return res;
    }

    private static void mkdirAndWriteFile(String filePathAndName, Map<String, String> res, ZipEntry zipEntry, ZipInputStream zipInputStream) throws IOException {
        File newFile = new File(filePathAndName);

        // 创建父目录
        new File(newFile.getParent()).mkdirs();
        res.put(zipEntry.getName(), filePathAndName);
        // 写入解压后的文件内容
        writeFileToBase(zipInputStream, newFile);
    }

    private static void writeFileToBase(ZipInputStream zipInputStream, File newFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(newFile)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = zipInputStream.read(buffer)) > 0) {
                fos.write(buffer, 0, len);
            }
        }
        zipInputStream.closeEntry();
    }


}


