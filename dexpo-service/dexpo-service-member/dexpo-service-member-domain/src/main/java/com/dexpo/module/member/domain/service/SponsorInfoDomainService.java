package com.dexpo.module.member.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dexpo.module.integration.api.message.dto.ValidCodeDTO;
import com.dexpo.module.member.api.dto.SponsorLoginDTO;
import com.dexpo.module.member.api.dto.SponsorLoginExhibitionTagDTO;
import com.dexpo.module.member.api.vo.SponsorLoginVO;
import com.dexpo.module.member.dal.dataobject.SponsorInfoDO;

public interface SponsorInfoDomainService extends IService<SponsorInfoDO> {

    /**
     * 登录验证code
     *
     * @param dto dto
     * @return true-success
     */
    Boolean loginValidCode(ValidCodeDTO dto);


    /**
     * 运营人员后台登录
     *
     * @param loginDTO login
     * @return 用户信息
     */
    SponsorLoginVO login(SponsorLoginDTO loginDTO);

    /**
     * 登出
     */
    void logout();

    /**
     * 运营人员选择展会code 记录redis
     *
     * @param req req
     */
    void chooseTag(SponsorLoginExhibitionTagDTO req);
} 