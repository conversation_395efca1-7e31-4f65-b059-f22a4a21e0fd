package com.dexpo.module.member.domain.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.member.dal.dataobject.MemberApproveRecordDO;
import com.dexpo.module.member.dal.mysql.MemberApproveRecordMapper;
import com.dexpo.module.member.domain.service.MemberApproveRecordDomainService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 用户信息审核记录 Service 实现类
 */
@Service
@Validated
public class MemberApproveRecordDomainServiceImpl extends ServiceImpl<MemberApproveRecordMapper, MemberApproveRecordDO> implements MemberApproveRecordDomainService {


} 