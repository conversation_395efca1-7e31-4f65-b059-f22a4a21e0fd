package com.dexpo.module.member.domain.repository;

import com.dexpo.module.member.domain.model.member.MemberReporterInfo;

import java.util.List;

public interface MemberReporterInfoRepository {
    MemberReporterInfo queryByMemberId(Long memberId);

    void addOrModify(MemberReporterInfo memberReporterInfo);

    void updateMediaPermissionType(List<Long> memberIds, String mediaPermissionType, Long exhibitionId);

    void addBatch(List<MemberReporterInfo> memberReporterInfos);
}
