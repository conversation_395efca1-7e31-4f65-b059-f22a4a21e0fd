package com.dexpo.module.member.domain.repository;

import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.member.domain.model.audience.AudiencePageList;
import com.dexpo.module.member.domain.model.audience.AudiencePageListQuery;
import com.dexpo.module.member.domain.model.media.MediaPageList;
import com.dexpo.module.member.domain.model.media.MediaPageListQuery;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;

import java.util.List;
import java.util.Set;

public interface MemberBaseInfoRepository {
    PageResult<AudiencePageList> getAudiencePage(AudiencePageListQuery query, String optionCode);

    void add(MemberBaseInfo baseInfo);

    MemberBaseInfo getMemberBaseInfoByMobileOrEmail(String loginTool);

    MemberBaseInfo getById(Long memberId);

    void addOrModify(MemberBaseInfo memberBaseInfo);

    List<MemberBaseInfo> queryAll();

    PageResult<MediaPageList> getMediaPage(MediaPageListQuery queryDO, String optionCode);

    List<MemberBaseInfo> queryListByPhoneAndEmail(Set<String> phoneSet, Set<String> emailSet);

    void addBatch(List<MemberBaseInfo> memberBaseInfos);

    /**
     * 根据id 更新会员信息
     *
     * @param memberBaseInfo 会员信息
     */
    void updateMemberById(MemberBaseInfo memberBaseInfo);

    /**
     * 根据id删除会员
     *
     * @param id id
     */
    void deleteMemberById(Long id);

    /**
     * 通过id查询
     *
     * @param id id
     * @return memberBaseInfo
     */
    MemberBaseInfo findById(Long id);


}
