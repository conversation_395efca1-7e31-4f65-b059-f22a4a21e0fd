package com.dexpo.module.member.api.vo.media;


import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.api.vo.member.MemberBaseInfoVO;
import com.dexpo.module.member.api.vo.member.MemberRecipientInfoVO;
import com.dexpo.module.member.api.vo.member.MemberReporterInfoVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "媒体注册信息VO对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MediaRegisterInfoVO {

    @Schema(description = "会员基础信息VO对象")
    private MemberBaseInfoVO memberBaseInfo;

    @Schema(description = "媒体用户拓展信息VO对象")
    private MemberReporterInfoVO memberReporterInfo;

    @Schema(description = "用户收证信息VO对象")
    private MemberRecipientInfoVO memberRecipientInfo;

    @Schema(description = "企业信息数据对象")
    private EnterpriseInfoVO enterpriseInfoVO;
}
