package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.member.domain.model.member.MemberParticipateRecord;
import com.dexpo.module.member.domain.repository.MemberParticipateRecordRepository;
import com.dexpo.module.member.infrastructure.converter.MemberParticipateRecordDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberParticipateRecordDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberParticipateRecordMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class MemberParticipateRecordRepositoryImpl extends ServiceImpl<MemberParticipateRecordMapper, MemberParticipateRecordDO> implements MemberParticipateRecordRepository {

    @Override
    public void active(List<Long> ids) {
        baseMapper.active(ids);
    }

    @Override
    public MemberParticipateRecord queryByUserIdAndExhibitionId(Long memberId, Long exhibitionId) {
        MemberParticipateRecordDO memberParticipateRecordDO = baseMapper.queryByUserIdAndExhibitionId(memberId, exhibitionId);
        return MemberParticipateRecordDoConverter.INSTANCE.toModel(memberParticipateRecordDO);
    }

    @Override
    public void modifyById(MemberParticipateRecord memberParticipateRecord) {
        MemberParticipateRecordDO memberParticipateRecordDO = MemberParticipateRecordDoConverter.INSTANCE.toDo(memberParticipateRecord);
        updateById(memberParticipateRecordDO);
    }

    @Override
    public void add(MemberParticipateRecord memberParticipateRecord) {
        MemberParticipateRecordDO memberParticipateRecordDO = MemberParticipateRecordDoConverter.INSTANCE.toDo(memberParticipateRecord);
        save(memberParticipateRecordDO);
    }

    @Override
    public void modifyBatch(List<MemberParticipateRecord> memberParticipateRecords) {
        List<MemberParticipateRecordDO> memberParticipateRecordDOS = MemberParticipateRecordDoConverter.INSTANCE.toDoList(memberParticipateRecords);
        updateBatchById(memberParticipateRecordDOS);
    }

    @Override
    public List<MemberParticipateRecord> queryByMemberIdAndExhibitionId(Set<Long> memberIdSet, Long exhibitionId) {
        LambdaQueryWrapper<MemberParticipateRecordDO> queryDO = new LambdaQueryWrapper<>();
        queryDO.in(MemberParticipateRecordDO::getMemberId, memberIdSet)
                .in(MemberParticipateRecordDO::getExhibitionId, exhibitionId);
        queryDO.eq(MemberParticipateRecordDO::getDelFlg, false);
        List<MemberParticipateRecordDO> memberParticipateRecordDOS = list(queryDO);
        return MemberParticipateRecordDoConverter.INSTANCE.toModelList(memberParticipateRecordDOS);
    }

    @Override
    public void addBatch(List<MemberParticipateRecord> memberParticipateRecords) {

    }

}
