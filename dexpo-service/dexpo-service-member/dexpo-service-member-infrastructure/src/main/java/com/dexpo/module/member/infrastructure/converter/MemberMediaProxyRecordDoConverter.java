package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.domain.model.member.MemberMediaProxyRecord;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberMediaProxyRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MemberMediaProxyRecordDoConverter {


    MemberMediaProxyRecordDoConverter INSTANCE = Mappers.getMapper(MemberMediaProxyRecordDoConverter.class);

    MemberMediaProxyRecord toModel(MemberMediaProxyRecordDO data);

    MemberMediaProxyRecordDO toDo(MemberMediaProxyRecord data);
}
