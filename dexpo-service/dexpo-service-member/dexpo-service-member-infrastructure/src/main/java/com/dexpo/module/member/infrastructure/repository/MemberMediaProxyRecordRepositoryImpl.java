package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.member.domain.model.member.MemberMediaProxyRecord;
import com.dexpo.module.member.domain.repository.MemberMediaProxyRecordRepository;
import com.dexpo.module.member.infrastructure.converter.MemberMediaProxyRecordDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberMediaProxyRecordDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberMediaProxyRecordMapper;
import org.springframework.stereotype.Component;

@Component
public class MemberMediaProxyRecordRepositoryImpl extends ServiceImpl<MemberMediaProxyRecordMapper, MemberMediaProxyRecordDO> implements MemberMediaProxyRecordRepository {

    @Override
    public MemberMediaProxyRecord queryById(Long memberMediaProxyRecordId) {
        MemberMediaProxyRecordDO memberMediaProxyRecordDO = getById(memberMediaProxyRecordId);
        return MemberMediaProxyRecordDoConverter.INSTANCE.toModel(memberMediaProxyRecordDO);
    }

    @Override
    public void addOrModify(MemberMediaProxyRecord memberMediaProxyRecord) {
        MemberMediaProxyRecordDO memberMediaProxyRecordDO = MemberMediaProxyRecordDoConverter.INSTANCE.toDo(memberMediaProxyRecord);
        saveOrUpdate(memberMediaProxyRecordDO);
    }
}
