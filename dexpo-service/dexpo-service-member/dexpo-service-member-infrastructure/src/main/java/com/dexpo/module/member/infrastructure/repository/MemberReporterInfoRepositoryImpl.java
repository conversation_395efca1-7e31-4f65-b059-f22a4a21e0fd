package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.member.domain.model.member.MemberReporterInfo;
import com.dexpo.module.member.domain.repository.MemberReporterInfoRepository;
import com.dexpo.module.member.infrastructure.converter.MemberReporterInfoDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberReporterInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberReporterInfoMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MemberReporterInfoRepositoryImpl extends ServiceImpl<MemberReporterInfoMapper, MemberReporterInfoDO> implements MemberReporterInfoRepository {

    @Override
    public MemberReporterInfo queryByMemberId(Long memberId) {
        MemberReporterInfoDO reporterInfoDO = baseMapper.queryByMemberId(memberId);
        return MemberReporterInfoDoConverter.INSTANCE.toModel(reporterInfoDO);
    }

    @Override
    public void addOrModify(MemberReporterInfo memberReporterInfo) {
        MemberReporterInfoDO memberReporterInfoDO = MemberReporterInfoDoConverter.INSTANCE.toDo(memberReporterInfo);
        saveOrUpdate(memberReporterInfoDO);
    }

    @Override
    public void updateMediaPermissionType(List<Long> memberIds, String mediaPermissionType, Long exhibitionId) {
        baseMapper.updateMediaPermissionType(memberIds, mediaPermissionType, exhibitionId);
    }

    @Override
    public void addBatch(List<MemberReporterInfo> memberReporterInfos) {
        List<MemberReporterInfoDO> memberReporterInfoDOS = MemberReporterInfoDoConverter.INSTANCE.toDoList(memberReporterInfos);
        saveBatch(memberReporterInfoDOS);
    }
}
