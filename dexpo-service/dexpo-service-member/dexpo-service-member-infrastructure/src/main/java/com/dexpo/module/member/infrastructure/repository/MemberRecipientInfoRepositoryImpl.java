package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.member.domain.model.member.MemberRecipientInfo;
import com.dexpo.module.member.domain.repository.MemberRecipientInfoRepository;
import com.dexpo.module.member.infrastructure.converter.MemberRecipientInfoDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberRecipientInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberRecipientInfoMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MemberRecipientInfoRepositoryImpl extends ServiceImpl<MemberRecipientInfoMapper, MemberRecipientInfoDO> implements MemberRecipientInfoRepository {

    @Override
    public MemberRecipientInfo queryByMemberId(Long memberId) {
        MemberRecipientInfoDO recipientInfoDO = baseMapper.queryByMemberId(memberId);
        return MemberRecipientInfoDoConverter.INSTANCE.toModel(recipientInfoDO);
    }

    @Override
    public void addOrModify(MemberRecipientInfo memberRecipientInfo) {
        MemberRecipientInfoDO memberRecipientInfoDO = MemberRecipientInfoDoConverter.INSTANCE.toDo(memberRecipientInfo);
        saveOrUpdate(memberRecipientInfoDO);
    }

    @Override
    public void addBatch(List<MemberRecipientInfo> memberRecipientInfos) {
        List<MemberRecipientInfoDO> memberRecipientInfoDOS = MemberRecipientInfoDoConverter.INSTANCE.toDoList(memberRecipientInfos);
        saveBatch(memberRecipientInfoDOS);
    }
}
