package com.dexpo.module.member.infrastructure.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dexpo.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业信息数据对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("enterprise_info")
public class EnterpriseInfoDO extends BaseDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 企业名称
     */
    @TableField("enterprise_name")
    private String enterpriseName;
    
    /**
     * 企业类型：值集VS_ACTION_ENTERPRISE_TYPE
     */
    @TableField("enterprise_type")
    private String enterpriseType;
    
    /**
     * 所属地域
     */
    @TableField("enterprise_location_code")
    private String enterpriseLocationCode;

    /**
     * 所属地域中文名称
     */
    @TableField("enterprise_location_name_cn")
    private String enterpriseLocationNameCn;

    /**
     * 所属地域英文名称
     */
    @TableField("enterprise_location_name_en")
    private String enterpriseLocationNameEn;

    /**
     * 是否可用：0否 1是
     */
    @TableField("is_use_able")
    private Boolean isUseAble;
} 