package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.cache.redis.entity.MemberBaseInfoCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.member.domain.model.audience.AudiencePageList;
import com.dexpo.module.member.domain.model.audience.AudiencePageListQuery;
import com.dexpo.module.member.domain.model.media.MediaPageList;
import com.dexpo.module.member.domain.model.media.MediaPageListQuery;
import com.dexpo.module.member.domain.model.member.MemberBaseInfo;
import com.dexpo.module.member.domain.repository.MemberBaseInfoRepository;
import com.dexpo.module.member.infrastructure.converter.MemberBaseInfoDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberBaseInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberBaseInfoMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
@RequiredArgsConstructor
public class MemberBaseInfoRepositoryImpl extends ServiceImpl<MemberBaseInfoMapper, MemberBaseInfoDO> implements MemberBaseInfoRepository {

    private final MemberBaseInfoOpt memberBaseInfoOpt;

    @Override
    public PageResult<AudiencePageList> getAudiencePage(AudiencePageListQuery query, String optionCode) {
        Page<AudiencePageList> objects = PageMethod.startPage(query.getPageNo(), query.getPageSize());
        baseMapper.getAudienceList(query, optionCode);
        return new PageResult<>(objects.getResult(), objects.getTotal());
    }

    @Override
    public void add(MemberBaseInfo baseInfo) {
        MemberBaseInfoDO memberBaseInfoDO = MemberBaseInfoDoConverter.INSTANCE.toDo(baseInfo);
        save(memberBaseInfoDO);
    }

    @Override
    public MemberBaseInfo getMemberBaseInfoByMobileOrEmail(String loginTool) {

        MemberBaseInfoCache baseInfoCache = memberBaseInfoOpt.getMemberBaseInfoCache(loginTool);
        if (ObjectUtils.isNotEmpty(baseInfoCache)) {
            return MemberBaseInfoDoConverter.INSTANCE.cacheToEntity(baseInfoCache);
        }
        baseInfoCache = memberBaseInfoOpt.getMemberBaseInfoCache(loginTool);
        if (ObjectUtils.isNotEmpty(baseInfoCache)) {
            return MemberBaseInfoDoConverter.INSTANCE.cacheToEntity(baseInfoCache);
        }

        MemberBaseInfoDO memberBaseInfoDO = baseMapper.getMemberBaseInfoByMobileOrEmail(loginTool);
        return MemberBaseInfoDoConverter.INSTANCE.toModel(memberBaseInfoDO);
    }

    @Override
    public MemberBaseInfo getById(Long memberId) {
        MemberBaseInfoDO baseInfoDO = baseMapper.selectById(memberId);
        return MemberBaseInfoDoConverter.INSTANCE.toModel(baseInfoDO);
    }

    @Override
    public void addOrModify(MemberBaseInfo memberBaseInfo) {
        MemberBaseInfoDO memberBaseInfoDO = MemberBaseInfoDoConverter.INSTANCE.toDo(memberBaseInfo);
        saveOrUpdate(memberBaseInfoDO);
    }

    @Override
    public List<MemberBaseInfo> queryAll() {
        List<MemberBaseInfoDO> memberBaseInfoDOS = baseMapper.selectList();
        return MemberBaseInfoDoConverter.INSTANCE.toModelList(memberBaseInfoDOS);
    }

    @Override
    public PageResult<MediaPageList> getMediaPage(MediaPageListQuery queryDO, String optionCode) {
        Page<MediaPageList> objects = PageMethod.startPage(queryDO.getPageNo(), queryDO.getPageSize());
        baseMapper.getMediaList(queryDO, ValueSetUserTypeEnum.MEDIA.getOptionCode());
        return new PageResult<>(objects.getResult(), objects.getTotal());
    }

    @Override
    public List<MemberBaseInfo> queryListByPhoneAndEmail(Set<String> phoneSet, Set<String> emailSet) {
        LambdaQueryWrapper<MemberBaseInfoDO> queryMemberBaseInfoDO = new LambdaQueryWrapper<>();
        queryMemberBaseInfoDO.in(MemberBaseInfoDO::getMemberMobile, phoneSet);
        queryMemberBaseInfoDO.in(MemberBaseInfoDO::getMemberEmail, emailSet);
        List<MemberBaseInfoDO> memberBaseInfoDOS = list(queryMemberBaseInfoDO);
        return MemberBaseInfoDoConverter.INSTANCE.toModelList(memberBaseInfoDOS);
    }

    @Override
    public void updateMemberById(MemberBaseInfo memberBaseInfo) {
        MemberBaseInfoDO memberBaseInfoDO = MemberBaseInfoDoConverter.INSTANCE.toDo(memberBaseInfo);
        updateById(memberBaseInfoDO);
    }

    @Override
    public void deleteMemberById(Long id) {
        removeById(id);
    }

    @Override
    public MemberBaseInfo findById(Long id) {
        return getById(id);
    }

    @Override
    public void addBatch(List<MemberBaseInfo> memberBaseInfos) {
        List<MemberBaseInfoDO> memberBaseInfoDOS = MemberBaseInfoDoConverter.INSTANCE.toDoList(memberBaseInfos);
        saveBatch(memberBaseInfoDOS);
    }
}
