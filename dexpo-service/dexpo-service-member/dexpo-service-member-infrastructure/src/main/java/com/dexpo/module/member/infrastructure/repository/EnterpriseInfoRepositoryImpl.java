package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfo;
import com.dexpo.module.member.domain.model.enterprise.EnterpriseInfoQuery;
import com.dexpo.module.member.domain.repository.EnterpriseInfoRepository;
import com.dexpo.module.member.infrastructure.converter.EnterpriseInfoDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.EnterpriseInfoDO;
import com.dexpo.module.member.infrastructure.dal.mysql.EnterpriseInfoMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EnterpriseInfoRepositoryImpl extends ServiceImpl<EnterpriseInfoMapper, EnterpriseInfoDO> implements EnterpriseInfoRepository {

    @Override
    public List<EnterpriseInfo> getEnterpriseInfoList(EnterpriseInfoQuery infoQuery) {
        LambdaQueryWrapperX<EnterpriseInfoDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(EnterpriseInfoDO::getIsUseAble, infoQuery.getIsUseAble());
        queryWrapper.likeIfPresent(EnterpriseInfoDO::getEnterpriseName, infoQuery.getEnterpriseName());
        queryWrapper.eq(EnterpriseInfoDO::getDelFlg, Boolean.FALSE);
        List<EnterpriseInfoDO> enterpriseInfoDOS = baseMapper.selectList(queryWrapper);
        return EnterpriseInfoDoConverter.INSTANCE.toModelList(enterpriseInfoDOS);
    }

    @Override
    public EnterpriseInfo getById(Long enterpriseId) {
        EnterpriseInfoDO enterpriseInfoDO = baseMapper.queryById(enterpriseId);
        return EnterpriseInfoDoConverter.INSTANCE.toModel(enterpriseInfoDO);
    }

    @Override
    public EnterpriseInfo queryMediaEnterpriseByNameAndLocationCode(String enterpriseName, String enterpriseLocationCode) {
        EnterpriseInfoDO enterpriseInfoDO = baseMapper.queryMediaEnterpriseByNameAndLocationCode(enterpriseName, enterpriseLocationCode);
        return EnterpriseInfoDoConverter.INSTANCE.toModel(enterpriseInfoDO);
    }

    @Override
    public void addOrModify(EnterpriseInfo enterpriseInfo) {
        EnterpriseInfoDO enterpriseInfoDO = EnterpriseInfoDoConverter.INSTANCE.toDo(enterpriseInfo);
        saveOrUpdate(enterpriseInfoDO);
    }

    @Override
    public List<EnterpriseInfo> queryAll() {
        List<EnterpriseInfoDO> enterpriseInfoDOS = list();
        return EnterpriseInfoDoConverter.INSTANCE.toModelList(enterpriseInfoDOS);
    }

    @Override
    public void addBatch(List<EnterpriseInfo> enterpriseInfos) {
        List<EnterpriseInfoDO> enterpriseInfoDOS = EnterpriseInfoDoConverter.INSTANCE.toDoList(enterpriseInfos);
        saveBatch(enterpriseInfoDOS);
    }
}
