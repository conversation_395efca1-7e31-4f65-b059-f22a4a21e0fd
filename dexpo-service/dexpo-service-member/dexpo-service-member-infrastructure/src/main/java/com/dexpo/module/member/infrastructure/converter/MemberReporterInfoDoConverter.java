package com.dexpo.module.member.infrastructure.converter;

import com.dexpo.module.member.domain.model.member.MemberReporterInfo;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberReporterInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberReporterInfoDoConverter {


    MemberReporterInfoDoConverter INSTANCE = Mappers.getMapper(MemberReporterInfoDoConverter.class);

    @Mappings({})
    MemberReporterInfo toModel(MemberReporterInfoDO data);

    MemberReporterInfoDO toDo(MemberReporterInfo data);

    List<MemberReporterInfoDO> toDoList(List<MemberReporterInfo> dataList);
}
