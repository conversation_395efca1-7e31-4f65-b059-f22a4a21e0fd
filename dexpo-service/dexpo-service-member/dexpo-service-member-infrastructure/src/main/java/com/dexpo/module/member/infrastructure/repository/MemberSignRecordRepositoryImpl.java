package com.dexpo.module.member.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.module.member.domain.model.member.MemberSignRecord;
import com.dexpo.module.member.domain.repository.MemberSignRecordRepository;
import com.dexpo.module.member.infrastructure.converter.MemberSignRecordDoConverter;
import com.dexpo.module.member.infrastructure.dal.dataobject.MemberSignRecordDO;
import com.dexpo.module.member.infrastructure.dal.mysql.MemberSignRecordMapper;
import org.springframework.stereotype.Component;

@Component
public class MemberSignRecordRepositoryImpl extends ServiceImpl<MemberSignRecordMapper, MemberSignRecordDO> implements MemberSignRecordRepository {

    @Override
    public MemberSignRecord queryByMemberIdAndAgreementId(Long memberId, Long agreementId) {
        MemberSignRecordDO memberSignRecordDO = baseMapper.queryByMemberIdAndAgreementId(memberId, agreementId);
        return MemberSignRecordDoConverter.INSTANCE.toModel(memberSignRecordDO);
    }

    @Override
    public void add(MemberSignRecord record) {
        MemberSignRecordDO memberSignRecordDO = MemberSignRecordDoConverter.INSTANCE.toDo(record);
        save(memberSignRecordDO);
    }
}
