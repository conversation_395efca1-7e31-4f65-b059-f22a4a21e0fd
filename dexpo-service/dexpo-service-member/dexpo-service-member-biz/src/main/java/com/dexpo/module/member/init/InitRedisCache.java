package com.dexpo.module.member.init;


import com.dexpo.module.member.service.MediaMemberService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 初始化相关缓存信息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class InitRedisCache {

    @Resource
    private final MediaMemberService mediaMemberService;


    @PostConstruct
    public void init() {
        initRedisCache();
    }


    private void initRedisCache() {
        log.info("init member info to redis start");
        mediaMemberService.initMediaMemberInfoToRedisCache();
        log.info("init member info to redis end");
    }
}
