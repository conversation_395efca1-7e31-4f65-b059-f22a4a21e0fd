package com.dexpo.module.member.controller;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.member.api.EnterpriseApi;
import com.dexpo.module.member.api.dto.EnterpriseInfoQueryDTO;
import com.dexpo.module.member.api.vo.EnterpriseInfoVO;
import com.dexpo.module.member.service.EnterpriseInfoService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Validated
public class EnterpriseController implements EnterpriseApi {

    @Resource
    private EnterpriseInfoService enterpriseInfoService;

    @Override
    public CommonResult<List<EnterpriseInfoVO>> getEnterpriseInfoList(EnterpriseInfoQueryDTO infoQueryDTO) {
        return CommonResult.success(enterpriseInfoService.getEnterpriseInfoList(infoQueryDTO));
    }
}
