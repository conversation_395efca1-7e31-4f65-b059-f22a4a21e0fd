package com.dexpo.module.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.module.log.api.dto.SysInterfaceLogDTO;
import com.dexpo.module.log.api.vo.SysInterfaceLogVO;
import com.dexpo.module.log.dal.dataobject.SysInterfaceLogDO;

/**
 * 展会信息 Service 接口
 */
public interface SysInterfaceLogService extends IService<SysInterfaceLogDO> {


    void insert(String message);

    PageResult<SysInterfaceLogVO> pageByDto(SysInterfaceLogDTO dto);
}