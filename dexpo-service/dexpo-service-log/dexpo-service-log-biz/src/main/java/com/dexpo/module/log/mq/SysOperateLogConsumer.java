package com.dexpo.module.log.mq;

import com.dexpo.module.log.service.SysOperateLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Consumer;

@Slf4j
@Configuration
public class SysOperateLogConsumer {

    @Resource
    private SysOperateLogService sysOperateLogService;

    @Bean()
    public Consumer<String> sysOperateLogChannel() {
        return message -> sysOperateLogService.insert(message);
    }
}
