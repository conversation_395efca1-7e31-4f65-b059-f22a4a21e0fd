package com.dexpo.module.log.api.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 展会协议信息 DO
 *
 * <AUTHOR>
 */
@Data
@ToString()
public class SysUploadDownloadRecordVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 下载文件地址
     */
    private String fileUrl;

    /**
     * 异步操作场景：值集VS_ASYN_SCENE
     */
    private String asynScene;

    /**
     * 操作人用户ID
     */
    private Long userId;

    /**
     * 操作人姓名
     */
    private String userName;

    /**
     * 操作人类型：值集VS_ACTION_USER_TYPE
     */
    private String userType;

    /**
     * 执行开始时间
     */
    private LocalDateTime doStartTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime doEndTime;

    /**
     * 异步操作状态：值集VS_ASYN_STATUS
     */
    private String status;

    /**
     * 备注
     */
    private String remarks;
} 