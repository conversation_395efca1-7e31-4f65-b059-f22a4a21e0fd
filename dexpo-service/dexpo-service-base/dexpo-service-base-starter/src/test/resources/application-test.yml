# 测试环境配置文件
# 用于单元测试和集成测试的配置

spring:
  profiles:
    active: test
  
  # 数据源配置（测试环境使用内存数据库）
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # H2数据库控制台（仅测试环境）
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
        
  # Redis配置（测试环境使用嵌入式Redis）
  redis:
    host: localhost
    port: 6379
    database: 15  # 使用独立的测试数据库
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
        
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000  # 10分钟
      
  # 日志配置
logging:
  level:
    com.dexpo.module.base: DEBUG
    org.springframework.web: INFO
    org.springframework.security: DEBUG
    org.mybatis: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    
# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      
# 测试专用配置
test:
  # 是否启用测试数据初始化
  data-init: true
  # 测试数据文件路径
  data-path: classpath:test-data/
  # 是否启用Mock服务
  mock-enabled: true
  
# 服务发现配置（测试环境禁用）
eureka:
  client:
    enabled: false
    
# Feign配置（测试环境）
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 5000
        
# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
      
# 安全配置（测试环境）
security:
  # 禁用CSRF（测试环境）
  csrf:
    enabled: false
  # JWT配置
  jwt:
    secret: test-secret-key-for-unit-testing-only
    expiration: 3600000  # 1小时
    
# 业务配置
business:
  # 地域配置
  location:
    # 默认地域标签
    default-tag: DEFAULT
    # 缓存过期时间（秒）
    cache-ttl: 300
  # 值集配置
  valueset:
    # 默认语言
    default-language: zh-CN
    # 缓存过期时间（秒）
    cache-ttl: 600
