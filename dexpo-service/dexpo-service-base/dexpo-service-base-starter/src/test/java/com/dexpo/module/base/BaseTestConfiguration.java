package com.dexpo.module.base;

// 注意：这里不直接导入具体的Mapper类，避免编译时依赖
// 在实际使用时会通过反射或者条件Bean的方式处理
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.sql.DataSource;

import static org.mockito.Mockito.mock;

/**
 * 基础测试配置类
 *
 * <p>该配置类为单元测试提供必要的Bean配置和Mock对象，
 * 确保测试环境的独立性和可控性。使用Mock对象替代真实的数据库和Redis连接。</p>
 *
 * <p>Mock对象包括：</p>
 * <ul>
 *   <li>数据库相关：DataSource、Mapper等</li>
 *   <li>Redis相关：RedisTemplate、StringRedisTemplate等</li>
 *   <li>安全框架：SecurityFrameworkUtils等</li>
 *   <li>外部服务：各种Feign客户端等</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TestConfiguration
@ActiveProfiles("test")
public class BaseTestConfiguration {

    // ==================== 数据库相关Mock ====================

    /**
     * 创建测试用的数据源Mock对象
     */
    @Bean
    @Primary
    public DataSource dataSource() {
        return mock(DataSource.class);
    }

    /**
     * 创建测试用的BasicLocationMapper Mock对象
     * 使用Object类型避免编译时依赖问题
     */
    @Bean
    @Primary
    public Object basicLocationMapper() {
        return mock(Object.class);
    }

    /**
     * 创建测试用的基础设施层BasicLocationMapper Mock对象
     * 使用Object类型避免编译时依赖问题
     */
    @Bean
    @Primary
    public Object infraBasicLocationMapper() {
        return mock(Object.class);
    }

    // ==================== Redis相关Mock ====================

    /**
     * 创建测试用的RedisTemplate Mock对象
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate() {
        return mock(RedisTemplate.class);
    }

    /**
     * 创建测试用的StringRedisTemplate Mock对象
     */
    @Bean
    @Primary
    public StringRedisTemplate stringRedisTemplate() {
        return mock(StringRedisTemplate.class);
    }

    // ==================== 框架相关Mock ====================

    /**
     * 创建测试用的安全框架工具Mock对象
     */
    @Bean
    @Primary
    public Object securityFrameworkUtils() {
        // 使用Object类型避免编译时依赖问题
        return mock(Object.class);
    }

    // ==================== 缓存相关Mock ====================

    /**
     * 创建测试用的缓存管理器Mock对象
     */
    @Bean
    @Primary
    public org.springframework.cache.CacheManager cacheManager() {
        return mock(org.springframework.cache.CacheManager.class);
    }
}
