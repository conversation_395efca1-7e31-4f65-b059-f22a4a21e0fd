package com.dexpo.module.base;

import com.dexpo.module.base.app.service.BasicValueSetAppServiceImplTest;
import com.dexpo.module.base.config.RestTemplateConfigTest;
import com.dexpo.module.base.controller.BasicLocationControllerTest;
import com.dexpo.module.base.dal.dataobject.BasicLocationDOTest;
import com.dexpo.module.base.domain.model.BasicLocationTest;
import com.dexpo.module.base.domain.service.BasicLocationDomainServiceTest;
import com.dexpo.module.base.enums.ApiConstantsTest;
import com.dexpo.module.base.enums.BasicRegionLevelEnumsTest;
import com.dexpo.module.base.enums.CommonTodoMediaRegisterActionEnumTest;
import com.dexpo.module.base.infrastructure.repository.BasicLocationRepositoryImplTest;
import com.dexpo.module.base.service.BasicLocationServiceTest;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 全部测试套件
 * 
 * <p>该测试套件包含了dexpo-service-base模块的所有单元测试类，
 * 用于一次性执行所有测试，确保代码的完整性和质量。</p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Suite
@SuiteDisplayName("DEXPO基础服务模块全部测试套件")
@SelectClasses({
    // API层测试
    ApiConstantsTest.class,
    BasicRegionLevelEnumsTest.class,
    CommonTodoMediaRegisterActionEnumTest.class,
    
    // 配置层测试
    RestTemplateConfigTest.class,
    
    // 控制器层测试
    BasicLocationControllerTest.class,
    
    // 服务层测试
    BasicLocationServiceTest.class,
    
    // 应用层测试
    BasicValueSetAppServiceImplTest.class,
    
    // 领域层测试
    BasicLocationTest.class,
    BasicLocationDomainServiceTest.class,
    
    // 基础设施层测试
    BasicLocationRepositoryImplTest.class,
    
    // 数据层测试
    BasicLocationDOTest.class,
    
    // 启动层测试
    BaseServerApplicationTest.class
})
public class AllTestSuite {
    // 测试套件不需要实现方法，所有配置通过注解完成
}
