package com.dexpo.module.base.app.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dexpo.framework.common.exception.enums.BaseServiceErrorCodeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.base.app.api.AttachmentInfoAppService;
import com.dexpo.module.base.app.converter.AttachmentInfoDTOConvert;
import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.domain.service.AttachmentInfoDomainService;
import com.dexpo.module.integration.api.flie.FileApi;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 值集选项 Service 实现类
 */
@Service
@Validated
public class AttachmentInfoAppServiceImpl implements AttachmentInfoAppService {

    @Resource
    private AttachmentInfoDomainService attachmentInfoDomainService;

    @Resource
    private FileApi fileApi;

    @SneakyThrows
    @Override
    public AttachmentInfoVO findById(Long id) {
        AttachmentInfo attachmentInfo = attachmentInfoDomainService.findById(id);
        return AttachmentInfoDTOConvert.INSTANCE.e2v(attachmentInfo);
    }

    @Override
    public AttachmentInfoVO createAttachment(AttachmentInfoDTO attachmentInfoDTO, LoginUser loginUser) {
        AttachmentInfo attachmentInfo = AttachmentInfoDTOConvert.INSTANCE.d2e(attachmentInfoDTO);
        attachmentInfo.setCreateUser(loginUser.getId());
        attachmentInfo.setCreateUserName(loginUser.getUserName());
        AttachmentInfo result = attachmentInfoDomainService.createAttachment(attachmentInfo);
        return AttachmentInfoDTOConvert.INSTANCE.e2v(result);
    }

    @Override
    public List<AttachmentInfoVO> findByIdList(List<Long> idList) {
        List<AttachmentInfo> attachmentInfoDOS = attachmentInfoDomainService.findByIdList(idList);
        return AttachmentInfoDTOConvert.INSTANCE.e2vList(attachmentInfoDOS);
    }

    @Override
    public CommonResult<AttachmentInfoVO> findFileByBusinessType(String businessType) {
        AttachmentInfo attachmentInfo = attachmentInfoDomainService.findFileByBusinessType(businessType);
        if(attachmentInfo == null){
            return  CommonResult.error(BaseServiceErrorCodeEnum.ATTACHMENT_NOT_FIND.getErrorCode());
        }
        return CommonResult.success(AttachmentInfoDTOConvert.INSTANCE.e2v(attachmentInfo));
    }

}