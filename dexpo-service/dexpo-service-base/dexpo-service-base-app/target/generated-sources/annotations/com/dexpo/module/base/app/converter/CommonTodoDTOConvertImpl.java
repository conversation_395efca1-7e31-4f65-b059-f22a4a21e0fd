package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.domain.model.agg.CommonTodo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:22:38+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class CommonTodoDTOConvertImpl implements CommonTodoDTOConvert {

    @Override
    public CommonTodoVO e2v(CommonTodo entity) {
        if ( entity == null ) {
            return null;
        }

        CommonTodoVO commonTodoVO = new CommonTodoVO();

        commonTodoVO.setId( entity.getId() );
        commonTodoVO.setTodoTitle( entity.getTodoTitle() );
        commonTodoVO.setBusinessType( entity.getBusinessType() );
        commonTodoVO.setBusinessNo( entity.getBusinessNo() );
        commonTodoVO.setUserType( entity.getUserType() );
        commonTodoVO.setUserId( entity.getUserId() );
        commonTodoVO.setStatus( entity.getStatus() );
        commonTodoVO.setGenerationTime( entity.getGenerationTime() );
        commonTodoVO.setDoneTime( entity.getDoneTime() );

        return commonTodoVO;
    }

    @Override
    public List<CommonTodoVO> e2v(List<CommonTodo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CommonTodoVO> list = new ArrayList<CommonTodoVO>( entityList.size() );
        for ( CommonTodo commonTodo : entityList ) {
            list.add( e2v( commonTodo ) );
        }

        return list;
    }
}
