package com.dexpo.module.base.app.converter;

import com.dexpo.module.base.api.basic.dto.BasicLocationDTO;
import com.dexpo.module.base.api.basic.vo.BasicLocationVO;
import com.dexpo.module.base.domain.model.agg.BasicLocation;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:22:38+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class BasicLocationDTOConvertImpl implements BasicLocationDTOConvert {

    @Override
    public BasicLocation d2e(BasicLocationDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicLocation basicLocation = new BasicLocation();

        basicLocation.setLocationTag( arg0.getLocationTag() );

        return basicLocation;
    }

    @Override
    public BasicLocationDTO e2d(BasicLocation arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicLocationDTO basicLocationDTO = new BasicLocationDTO();

        basicLocationDTO.setLocationTag( arg0.getLocationTag() );

        return basicLocationDTO;
    }

    @Override
    public BasicLocationVO e2v(BasicLocation arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicLocationVO basicLocationVO = new BasicLocationVO();

        basicLocationVO.setId( arg0.getId() );
        basicLocationVO.setLocationCode( arg0.getLocationCode() );
        basicLocationVO.setLocationNameCn( arg0.getLocationNameCn() );
        basicLocationVO.setLocationNameEn( arg0.getLocationNameEn() );
        basicLocationVO.setLocationTag( arg0.getLocationTag() );

        return basicLocationVO;
    }

    @Override
    public BasicLocation v2e(BasicLocationVO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicLocation basicLocation = new BasicLocation();

        basicLocation.setId( arg0.getId() );
        basicLocation.setLocationCode( arg0.getLocationCode() );
        basicLocation.setLocationNameCn( arg0.getLocationNameCn() );
        basicLocation.setLocationNameEn( arg0.getLocationNameEn() );
        basicLocation.setLocationTag( arg0.getLocationTag() );

        return basicLocation;
    }

    @Override
    public BasicLocationVO d2v(BasicLocationDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicLocationVO basicLocationVO = new BasicLocationVO();

        basicLocationVO.setLocationTag( arg0.getLocationTag() );

        return basicLocationVO;
    }

    @Override
    public BasicLocationDTO v2d(BasicLocationVO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicLocationDTO basicLocationDTO = new BasicLocationDTO();

        basicLocationDTO.setLocationTag( arg0.getLocationTag() );

        return basicLocationDTO;
    }

    @Override
    public List<BasicLocation> d2eList(List<BasicLocationDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicLocation> list = new ArrayList<BasicLocation>( arg0.size() );
        for ( BasicLocationDTO basicLocationDTO : arg0 ) {
            list.add( d2e( basicLocationDTO ) );
        }

        return list;
    }

    @Override
    public List<BasicLocationDTO> e2dList(List<BasicLocation> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicLocationDTO> list = new ArrayList<BasicLocationDTO>( arg0.size() );
        for ( BasicLocation basicLocation : arg0 ) {
            list.add( e2d( basicLocation ) );
        }

        return list;
    }

    @Override
    public List<BasicLocationVO> e2vList(List<BasicLocation> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicLocationVO> list = new ArrayList<BasicLocationVO>( arg0.size() );
        for ( BasicLocation basicLocation : arg0 ) {
            list.add( e2v( basicLocation ) );
        }

        return list;
    }

    @Override
    public List<BasicLocation> v2eList(List<BasicLocationVO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicLocation> list = new ArrayList<BasicLocation>( arg0.size() );
        for ( BasicLocationVO basicLocationVO : arg0 ) {
            list.add( v2e( basicLocationVO ) );
        }

        return list;
    }

    @Override
    public List<BasicLocationVO> d2vList(List<BasicLocationDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicLocationVO> list = new ArrayList<BasicLocationVO>( arg0.size() );
        for ( BasicLocationDTO basicLocationDTO : arg0 ) {
            list.add( d2v( basicLocationDTO ) );
        }

        return list;
    }

    @Override
    public List<BasicLocationDTO> v2dList(List<BasicLocationVO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicLocationDTO> list = new ArrayList<BasicLocationDTO>( arg0.size() );
        for ( BasicLocationVO basicLocationVO : arg0 ) {
            list.add( v2d( basicLocationVO ) );
        }

        return list;
    }
}
