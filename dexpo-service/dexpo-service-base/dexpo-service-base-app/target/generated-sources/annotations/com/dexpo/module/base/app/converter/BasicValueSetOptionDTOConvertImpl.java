package com.dexpo.module.base.app.converter;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.module.base.api.basic.dto.BasicValuesetOptionDTO;
import com.dexpo.module.base.api.basic.vo.BasicValuesetOptionVO;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:22:38+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class BasicValueSetOptionDTOConvertImpl implements BasicValueSetOptionDTOConvert {

    @Override
    public BasicValueSetOption d2e(BasicValuesetOptionDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicValueSetOption basicValueSetOption = new BasicValueSetOption();

        basicValueSetOption.setValuesetCode( arg0.getValuesetCode() );
        basicValueSetOption.setOptionCode( arg0.getOptionCode() );
        basicValueSetOption.setOptionDescriptionCn( arg0.getOptionDescriptionCn() );
        basicValueSetOption.setOptionDescriptionEn( arg0.getOptionDescriptionEn() );
        basicValueSetOption.setOptionOrder( arg0.getOptionOrder() );
        basicValueSetOption.setParentCode( arg0.getParentCode() );

        return basicValueSetOption;
    }

    @Override
    public BasicValuesetOptionDTO e2d(BasicValueSetOption arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicValuesetOptionDTO basicValuesetOptionDTO = new BasicValuesetOptionDTO();

        basicValuesetOptionDTO.setValuesetCode( arg0.getValuesetCode() );
        basicValuesetOptionDTO.setOptionCode( arg0.getOptionCode() );
        basicValuesetOptionDTO.setOptionDescriptionCn( arg0.getOptionDescriptionCn() );
        basicValuesetOptionDTO.setOptionDescriptionEn( arg0.getOptionDescriptionEn() );
        basicValuesetOptionDTO.setOptionOrder( arg0.getOptionOrder() );
        basicValuesetOptionDTO.setParentCode( arg0.getParentCode() );

        return basicValuesetOptionDTO;
    }

    @Override
    public BasicValuesetOptionVO e2v(BasicValueSetOption arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicValuesetOptionVO basicValuesetOptionVO = new BasicValuesetOptionVO();

        basicValuesetOptionVO.setValuesetCode( arg0.getValuesetCode() );
        basicValuesetOptionVO.setOptionCode( arg0.getOptionCode() );
        basicValuesetOptionVO.setOptionDescriptionCn( arg0.getOptionDescriptionCn() );
        basicValuesetOptionVO.setOptionDescriptionEn( arg0.getOptionDescriptionEn() );
        basicValuesetOptionVO.setOptionOrder( arg0.getOptionOrder() );
        basicValuesetOptionVO.setParentCode( arg0.getParentCode() );

        return basicValuesetOptionVO;
    }

    @Override
    public BasicValueSetOption v2e(BasicValuesetOptionVO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicValueSetOption basicValueSetOption = new BasicValueSetOption();

        basicValueSetOption.setValuesetCode( arg0.getValuesetCode() );
        basicValueSetOption.setOptionCode( arg0.getOptionCode() );
        basicValueSetOption.setOptionDescriptionCn( arg0.getOptionDescriptionCn() );
        basicValueSetOption.setOptionDescriptionEn( arg0.getOptionDescriptionEn() );
        basicValueSetOption.setOptionOrder( arg0.getOptionOrder() );
        basicValueSetOption.setParentCode( arg0.getParentCode() );

        return basicValueSetOption;
    }

    @Override
    public BasicValuesetOptionVO d2v(BasicValuesetOptionDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicValuesetOptionVO basicValuesetOptionVO = new BasicValuesetOptionVO();

        basicValuesetOptionVO.setValuesetCode( arg0.getValuesetCode() );
        basicValuesetOptionVO.setOptionCode( arg0.getOptionCode() );
        basicValuesetOptionVO.setOptionDescriptionCn( arg0.getOptionDescriptionCn() );
        basicValuesetOptionVO.setOptionDescriptionEn( arg0.getOptionDescriptionEn() );
        basicValuesetOptionVO.setOptionOrder( arg0.getOptionOrder() );
        basicValuesetOptionVO.setParentCode( arg0.getParentCode() );

        return basicValuesetOptionVO;
    }

    @Override
    public BasicValuesetOptionDTO v2d(BasicValuesetOptionVO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        BasicValuesetOptionDTO basicValuesetOptionDTO = new BasicValuesetOptionDTO();

        basicValuesetOptionDTO.setValuesetCode( arg0.getValuesetCode() );
        basicValuesetOptionDTO.setOptionCode( arg0.getOptionCode() );
        basicValuesetOptionDTO.setOptionDescriptionCn( arg0.getOptionDescriptionCn() );
        basicValuesetOptionDTO.setOptionDescriptionEn( arg0.getOptionDescriptionEn() );
        basicValuesetOptionDTO.setOptionOrder( arg0.getOptionOrder() );
        basicValuesetOptionDTO.setParentCode( arg0.getParentCode() );

        return basicValuesetOptionDTO;
    }

    @Override
    public List<BasicValueSetOption> d2eList(List<BasicValuesetOptionDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicValueSetOption> list = new ArrayList<BasicValueSetOption>( arg0.size() );
        for ( BasicValuesetOptionDTO basicValuesetOptionDTO : arg0 ) {
            list.add( d2e( basicValuesetOptionDTO ) );
        }

        return list;
    }

    @Override
    public List<BasicValuesetOptionDTO> e2dList(List<BasicValueSetOption> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicValuesetOptionDTO> list = new ArrayList<BasicValuesetOptionDTO>( arg0.size() );
        for ( BasicValueSetOption basicValueSetOption : arg0 ) {
            list.add( e2d( basicValueSetOption ) );
        }

        return list;
    }

    @Override
    public List<BasicValuesetOptionVO> e2vList(List<BasicValueSetOption> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicValuesetOptionVO> list = new ArrayList<BasicValuesetOptionVO>( arg0.size() );
        for ( BasicValueSetOption basicValueSetOption : arg0 ) {
            list.add( e2v( basicValueSetOption ) );
        }

        return list;
    }

    @Override
    public List<BasicValueSetOption> v2eList(List<BasicValuesetOptionVO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicValueSetOption> list = new ArrayList<BasicValueSetOption>( arg0.size() );
        for ( BasicValuesetOptionVO basicValuesetOptionVO : arg0 ) {
            list.add( v2e( basicValuesetOptionVO ) );
        }

        return list;
    }

    @Override
    public List<BasicValuesetOptionVO> d2vList(List<BasicValuesetOptionDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicValuesetOptionVO> list = new ArrayList<BasicValuesetOptionVO>( arg0.size() );
        for ( BasicValuesetOptionDTO basicValuesetOptionDTO : arg0 ) {
            list.add( d2v( basicValuesetOptionDTO ) );
        }

        return list;
    }

    @Override
    public List<BasicValuesetOptionDTO> v2dList(List<BasicValuesetOptionVO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<BasicValuesetOptionDTO> list = new ArrayList<BasicValuesetOptionDTO>( arg0.size() );
        for ( BasicValuesetOptionVO basicValuesetOptionVO : arg0 ) {
            list.add( v2d( basicValuesetOptionVO ) );
        }

        return list;
    }

    @Override
    public BasicValuesetOptionCache toBasicValuesetOptionCache(BasicValueSetOption optionDO) {
        if ( optionDO == null ) {
            return null;
        }

        BasicValuesetOptionCache basicValuesetOptionCache = new BasicValuesetOptionCache();

        basicValuesetOptionCache.setId( optionDO.getId() );
        basicValuesetOptionCache.setValuesetCode( optionDO.getValuesetCode() );
        basicValuesetOptionCache.setOptionCode( optionDO.getOptionCode() );
        basicValuesetOptionCache.setOptionDescriptionCn( optionDO.getOptionDescriptionCn() );
        basicValuesetOptionCache.setOptionDescriptionEn( optionDO.getOptionDescriptionEn() );
        basicValuesetOptionCache.setOptionOrder( optionDO.getOptionOrder() );
        basicValuesetOptionCache.setParentCode( optionDO.getParentCode() );

        return basicValuesetOptionCache;
    }

    @Override
    public List<BasicValuesetOptionCache> toBasicValuesetOptionCache(List<BasicValueSetOption> optionDOs) {
        if ( optionDOs == null ) {
            return null;
        }

        List<BasicValuesetOptionCache> list = new ArrayList<BasicValuesetOptionCache>( optionDOs.size() );
        for ( BasicValueSetOption basicValueSetOption : optionDOs ) {
            list.add( toBasicValuesetOptionCache( basicValueSetOption ) );
        }

        return list;
    }

    @Override
    public BasicValuesetOptionVO cache2VO(BasicValuesetOptionCache cache) {
        if ( cache == null ) {
            return null;
        }

        BasicValuesetOptionVO basicValuesetOptionVO = new BasicValuesetOptionVO();

        basicValuesetOptionVO.setValuesetCode( cache.getValuesetCode() );
        basicValuesetOptionVO.setOptionCode( cache.getOptionCode() );
        basicValuesetOptionVO.setOptionDescriptionCn( cache.getOptionDescriptionCn() );
        basicValuesetOptionVO.setOptionDescriptionEn( cache.getOptionDescriptionEn() );
        basicValuesetOptionVO.setOptionOrder( cache.getOptionOrder() );
        basicValuesetOptionVO.setParentCode( cache.getParentCode() );

        return basicValuesetOptionVO;
    }

    @Override
    public List<BasicValuesetOptionVO> cache2VO(List<BasicValuesetOptionCache> caches) {
        if ( caches == null ) {
            return null;
        }

        List<BasicValuesetOptionVO> list = new ArrayList<BasicValuesetOptionVO>( caches.size() );
        for ( BasicValuesetOptionCache basicValuesetOptionCache : caches ) {
            list.add( cache2VO( basicValuesetOptionCache ) );
        }

        return list;
    }
}
