package com.dexpo.module.base.enums;

import com.dexpo.module.member.enums.RegisterStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 媒体注册动作
 */
@Getter
@AllArgsConstructor
public enum CommonTodoMediaRegisterActionEnum {

    SUBMIT(RegisterStatusEnum.PENDING_REVIEW, "媒体注册报名", "申请待审核"),

    ;

    private RegisterStatusEnum registerStatusEnum;

    private String actionDesc;

    private String typeDesc;

}