package com.dexpo.module.base.convert.basic;

import com.dexpo.module.base.dal.dataobject.basic.ExhibitionValuesetOptionRelationDO;
import com.dexpo.module.base.service.basic.entity.ExhibitionValuesetOptionRelationCache;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 会展tag关联值集信息转换接口
 */
@Mapper
public interface ExhibitionValuesetOptionConvert {

    ExhibitionValuesetOptionConvert INSTANCE = Mappers.getMapper(ExhibitionValuesetOptionConvert.class);


    /**
     * 转换为缓存对象
     *
     * @param relationDO do
     * @return cache
     */
    ExhibitionValuesetOptionRelationCache toCache(ExhibitionValuesetOptionRelationDO relationDO);

    /**
     * 转换为缓存对象
     *
     * @param relationDO do
     * @return cache
     */
    List<ExhibitionValuesetOptionRelationCache> toCache(List<ExhibitionValuesetOptionRelationDO> relationDO);
}