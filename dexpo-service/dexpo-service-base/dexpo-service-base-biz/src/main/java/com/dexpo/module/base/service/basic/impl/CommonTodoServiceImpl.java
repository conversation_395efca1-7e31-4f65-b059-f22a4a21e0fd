package com.dexpo.module.base.service.basic.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dexpo.framework.cache.redis.entity.sponsor.SponsorProfileCache;
import com.dexpo.framework.cache.redis.operate.member.MemberBaseInfoOpt;
import com.dexpo.framework.common.enums.ValueSetBusinessTypeEnum;
import com.dexpo.framework.common.enums.ValueSetTodoStatusEnum;
import com.dexpo.framework.common.enums.ValueSetUserTypeEnum;
import com.dexpo.framework.common.pojo.PageResult;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.module.base.convert.CommonTodoConvert;
import com.dexpo.module.base.dal.dataobject.basic.CommonTodoDO;
import com.dexpo.module.base.dal.mysql.basic.CommonTodoMapper;
import com.dexpo.module.base.enums.CommonTodoMediaRegisterActionEnum;
import com.dexpo.module.base.external.exhibition.ExhibitionExternalService;
import com.dexpo.module.base.service.basic.CommonTodoService;
import com.dexpo.module.exhibition.api.dto.ExhibitionQueryDTO;
import com.dexpo.module.exhibition.api.vo.ExhibitionVO;
import com.dexpo.module.member.api.dto.message.MediaRegisterEventDTO;
import com.dexpo.module.member.enums.RegisterStatusEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommonTodoServiceImpl extends ServiceImpl<CommonTodoMapper, CommonTodoDO> implements CommonTodoService {


    private final ExhibitionExternalService exhibitionExternalService;


    private final MemberBaseInfoOpt memberBaseInfoOpt;


    @Override
    public PageResult<CommonTodoVO> getPage(CommenTodoPageQueryDTO req) {
        // 需要获取对应展会下面的数据
        ExhibitionQueryDTO queryDTO = new ExhibitionQueryDTO();
        SponsorProfileCache profileCache = memberBaseInfoOpt.sponsorProfile(SecurityFrameworkUtils.getLoginUserId());
        queryDTO.setExhibitionTagCodes(Lists.newArrayList(profileCache.getExhibitionTagCode()));
        List<Long> exhibitionIds = exhibitionExternalService.getExhibitionIds(queryDTO);
        if (CollectionUtils.isEmpty(exhibitionIds)) {
            return PageResult.empty();
        }
        if (req.getStatus() == null) {
            req.setStatus(ValueSetTodoStatusEnum.TODO.getOptionCode());
        }
        try (Page<CommonTodoDO> page = PageMethod.startPage(req.getPageNo(),
                req.getPageSize())) {
            this.lambdaQuery().eq(CommonTodoDO::getStatus, req.getStatus()).orderByDesc(CommonTodoDO::getGenerationTime)
                    .list();
            List<CommonTodoVO> voList = CommonTodoConvert.INSTANCE.e2v(page.getResult());

            return new PageResult<>(voList, page.getTotal());
        } catch (Exception e) {
            log.info("comment todo page query error", e);
            throw e;
        }

    }

    @Override
    public void createMediaRegisterCommonTodo(MediaRegisterEventDTO eventDTO) {
        Long exhibitionId = eventDTO.getExhibitionId();
        ExhibitionVO exhibitionVO = exhibitionExternalService.getExhibition(exhibitionId);
        // 拼接待办标题 展会名称（已经包含了届数信息）+媒体名称+行为（媒体注册报名）+申请待审核
        String title = exhibitionVO.getExhibitionNameCn()
                + eventDTO.getEnterpriseName()
                + CommonTodoMediaRegisterActionEnum.SUBMIT.getActionDesc()
                + CommonTodoMediaRegisterActionEnum.SUBMIT.getTypeDesc();
        CommonTodoDO todoDO = new CommonTodoDO();
        todoDO.setTodoTitle(title);
        todoDO.setBusinessNo(String.valueOf(eventDTO.getMemberId()));
        todoDO.setBusinessType(ValueSetBusinessTypeEnum.MEDIA_USER_AUDIT.getOptionCode());
        todoDO.setExhibitionId(exhibitionId);
        todoDO.setUserType(ValueSetUserTypeEnum.SPONSOR.getOptionCode());
        todoDO.setStatus(ValueSetTodoStatusEnum.TODO.getOptionCode());
        todoDO.setUserId(eventDTO.getOperatorId());
        todoDO.setCreateUser(eventDTO.getOperatorId());
        todoDO.setCreateUserName(eventDTO.getOperatorName());
        todoDO.setUpdateUser(eventDTO.getOperatorId());
        todoDO.setUpdateUserName(eventDTO.getOperatorName());
        LocalDateTime parse = LocalDateTimeUtil.parse(eventDTO.getEventTime(), DatePattern.NORM_DATETIME_PATTERN);
        todoDO.setGenerationTime(parse);
        save(todoDO);
    }

    @Override
    public void updateMediaRegisterCommonTodo(MediaRegisterEventDTO eventDTO) {
        CommonTodoDO one = lambdaQuery().eq(CommonTodoDO::getBusinessNo, eventDTO.getMemberId())
                .eq(CommonTodoDO::getBusinessType, ValueSetBusinessTypeEnum.MEDIA_USER_AUDIT.getOptionCode())
                .eq(CommonTodoDO::getExhibitionId, eventDTO.getExhibitionId())
                .eq(CommonTodoDO::getStatus, ValueSetTodoStatusEnum.TODO.getOptionCode())
                .orderByDesc(CommonTodoDO::getGenerationTime)
                .last("limit 1").one();
        if (one == null) {
            log.warn("updateMediaRegisterCommonTodo 未获取到待办信息 params:{}", JSON.toJSONString(eventDTO));
            return;
        }
        String status;
        if (Objects.equals(eventDTO.getRegisterStatus(), RegisterStatusEnum.APPROVED.getCode())
                || Objects.equals(eventDTO.getRegisterStatus(), RegisterStatusEnum.REJECTED.getCode()))
            status = ValueSetTodoStatusEnum.DONE.getOptionCode();
        else if (Objects.equals(eventDTO.getRegisterStatus(), RegisterStatusEnum.RECALL.getCode()))
            status = ValueSetTodoStatusEnum.CANCEL.getOptionCode();
        else {
            log.warn("updateMediaRegisterCommonTodo 未知的媒体注册状态 params:{}", JSON.toJSONString(eventDTO));
            return;
        }
        CommonTodoDO todoDO = new CommonTodoDO();
        todoDO.setId(one.getId());
        todoDO.setStatus(status);
        todoDO.setUpdateUser(eventDTO.getOperatorId());
        todoDO.setUpdateUserName(eventDTO.getOperatorName());
        updateById(todoDO);
    }

}
