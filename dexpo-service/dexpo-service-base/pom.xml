<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dexpo</groupId>
        <artifactId>dexpo-framework-starter-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../dexpo-framework/dexpo-framework-starter-parent/pom.xml</relativePath>
    </parent>

    <groupId>com.dexpo</groupId>
    <artifactId>dexpo-service-base</artifactId>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>

    <modules>
        <module>dexpo-service-base-api</module>
        <module>dexpo-service-base-biz</module>
        <module>dexpo-service-base-app</module>
        <module>dexpo-service-base-domain</module>
        <module>dexpo-service-base-infrastructure</module>
        <module>dexpo-service-base-starter</module>
        <module>dexpo-service-base-entry</module>
    </modules>

    <description>
        infra 模块，主要提供两块能力：
        1. 我们放基础设施的运维与管理，支撑上层的通用与核心业务。 例如说：定时任务的管理、服务器的信息等等
        2. 研发工具，提升研发效率与质量。 例如说：代码生成器、接口文档等等
    </description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-base-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-base-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-base-entry</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-base-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-base-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
