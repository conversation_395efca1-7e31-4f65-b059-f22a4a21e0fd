package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicLocationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BasicLocationConvert {

    BasicLocationConvert INSTANCE = Mappers.getMapper(BasicLocationConvert.class);

    BasicLocationDO d2e(BasicLocation basicLocation);

    BasicLocation e2d(BasicLocationDO entity);

    List<BasicLocation> e2dList(List<BasicLocationDO> doList);
}
