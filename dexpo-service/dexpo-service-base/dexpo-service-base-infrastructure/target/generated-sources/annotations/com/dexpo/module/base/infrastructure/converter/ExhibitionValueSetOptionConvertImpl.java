package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.ExhibitionValuesetOptionRelation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.ExhibitionValuesetOptionRelationDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:25:41+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class ExhibitionValueSetOptionConvertImpl implements ExhibitionValueSetOptionConvert {

    @Override
    public ExhibitionValuesetOptionRelationDO d2e(ExhibitionValuesetOptionRelation relation) {
        if ( relation == null ) {
            return null;
        }

        ExhibitionValuesetOptionRelationDO exhibitionValuesetOptionRelationDO = new ExhibitionValuesetOptionRelationDO();

        exhibitionValuesetOptionRelationDO.setId( relation.getId() );
        exhibitionValuesetOptionRelationDO.setExhibitionTagCode( relation.getExhibitionTagCode() );
        exhibitionValuesetOptionRelationDO.setValuesetCode( relation.getValuesetCode() );
        exhibitionValuesetOptionRelationDO.setValuesetOptionCode( relation.getValuesetOptionCode() );

        return exhibitionValuesetOptionRelationDO;
    }

    @Override
    public ExhibitionValuesetOptionRelation e2d(ExhibitionValuesetOptionRelationDO entity) {
        if ( entity == null ) {
            return null;
        }

        ExhibitionValuesetOptionRelation exhibitionValuesetOptionRelation = new ExhibitionValuesetOptionRelation();

        exhibitionValuesetOptionRelation.setId( entity.getId() );
        exhibitionValuesetOptionRelation.setExhibitionTagCode( entity.getExhibitionTagCode() );
        exhibitionValuesetOptionRelation.setValuesetCode( entity.getValuesetCode() );
        exhibitionValuesetOptionRelation.setValuesetOptionCode( entity.getValuesetOptionCode() );

        return exhibitionValuesetOptionRelation;
    }

    @Override
    public List<ExhibitionValuesetOptionRelation> e2dList(List<ExhibitionValuesetOptionRelationDO> doList) {
        if ( doList == null ) {
            return null;
        }

        List<ExhibitionValuesetOptionRelation> list = new ArrayList<ExhibitionValuesetOptionRelation>( doList.size() );
        for ( ExhibitionValuesetOptionRelationDO exhibitionValuesetOptionRelationDO : doList ) {
            list.add( e2d( exhibitionValuesetOptionRelationDO ) );
        }

        return list;
    }
}
