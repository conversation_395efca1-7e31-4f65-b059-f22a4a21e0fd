package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.AttachmentInfo;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.AttachmentInfoDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:22:34+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class AttachmentInfoConverterImpl implements AttachmentInfoConverter {

    @Override
    public AttachmentInfo toEntity(AttachmentInfoDO attachmentInfoDO) {
        if ( attachmentInfoDO == null ) {
            return null;
        }

        AttachmentInfo attachmentInfo = new AttachmentInfo();

        attachmentInfo.setCreateUserName( attachmentInfoDO.getCreateUserName() );
        attachmentInfo.setCreateUser( attachmentInfoDO.getCreateUser() );

        return attachmentInfo;
    }

    @Override
    public List<AttachmentInfo> toEntityList(List<AttachmentInfoDO> attachmentInfoDOList) {
        if ( attachmentInfoDOList == null ) {
            return null;
        }

        List<AttachmentInfo> list = new ArrayList<AttachmentInfo>( attachmentInfoDOList.size() );
        for ( AttachmentInfoDO attachmentInfoDO : attachmentInfoDOList ) {
            list.add( toEntity( attachmentInfoDO ) );
        }

        return list;
    }

    @Override
    public AttachmentInfoDO toDO(AttachmentInfo attachmentInfo) {
        if ( attachmentInfo == null ) {
            return null;
        }

        AttachmentInfoDO attachmentInfoDO = new AttachmentInfoDO();

        attachmentInfoDO.setCreateUserName( attachmentInfo.getCreateUserName() );
        attachmentInfoDO.setCreateUser( attachmentInfo.getCreateUser() );

        return attachmentInfoDO;
    }
}
