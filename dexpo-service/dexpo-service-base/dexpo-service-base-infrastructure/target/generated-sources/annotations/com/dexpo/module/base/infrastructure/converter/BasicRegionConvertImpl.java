package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.BasicRegion;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicRegionDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:30:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class BasicRegionConvertImpl implements BasicRegionConvert {

    @Override
    public BasicRegionDO d2e(BasicRegion baseRegion) {
        if ( baseRegion == null ) {
            return null;
        }

        BasicRegionDO.BasicRegionDOBuilder basicRegionDO = BasicRegionDO.builder();

        basicRegionDO.id( baseRegion.getId() );
        basicRegionDO.parentAdcode( baseRegion.getParentAdcode() );
        basicRegionDO.level( baseRegion.getLevel() );
        basicRegionDO.adcode( baseRegion.getAdcode() );
        basicRegionDO.citycode( baseRegion.getCitycode() );
        basicRegionDO.name( baseRegion.getName() );
        basicRegionDO.nameEn( baseRegion.getNameEn() );
        basicRegionDO.center( baseRegion.getCenter() );
        basicRegionDO.polyline( baseRegion.getPolyline() );
        basicRegionDO.remark( baseRegion.getRemark() );

        return basicRegionDO.build();
    }

    @Override
    public BasicRegion e2d(BasicRegionDO entity) {
        if ( entity == null ) {
            return null;
        }

        BasicRegion basicRegion = new BasicRegion();

        basicRegion.setId( entity.getId() );
        basicRegion.setParentAdcode( entity.getParentAdcode() );
        basicRegion.setLevel( entity.getLevel() );
        basicRegion.setAdcode( entity.getAdcode() );
        basicRegion.setCitycode( entity.getCitycode() );
        basicRegion.setName( entity.getName() );
        basicRegion.setNameEn( entity.getNameEn() );
        basicRegion.setCenter( entity.getCenter() );
        basicRegion.setPolyline( entity.getPolyline() );
        basicRegion.setRemark( entity.getRemark() );

        return basicRegion;
    }

    @Override
    public List<BasicRegion> e2dList(List<BasicRegionDO> doList) {
        if ( doList == null ) {
            return null;
        }

        List<BasicRegion> list = new ArrayList<BasicRegion>( doList.size() );
        for ( BasicRegionDO basicRegionDO : doList ) {
            list.add( e2d( basicRegionDO ) );
        }

        return list;
    }
}
