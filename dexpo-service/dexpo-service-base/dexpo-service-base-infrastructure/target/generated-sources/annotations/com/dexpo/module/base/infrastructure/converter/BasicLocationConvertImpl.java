package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.BasicLocation;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicLocationDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:30:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class BasicLocationConvertImpl implements BasicLocationConvert {

    @Override
    public BasicLocationDO d2e(BasicLocation basicLocation) {
        if ( basicLocation == null ) {
            return null;
        }

        BasicLocationDO.BasicLocationDOBuilder basicLocationDO = BasicLocationDO.builder();

        basicLocationDO.id( basicLocation.getId() );
        basicLocationDO.locationCode( basicLocation.getLocationCode() );
        basicLocationDO.locationNameCn( basicLocation.getLocationNameCn() );
        basicLocationDO.locationNameEn( basicLocation.getLocationNameEn() );
        basicLocationDO.locationTag( basicLocation.getLocationTag() );

        return basicLocationDO.build();
    }

    @Override
    public BasicLocation e2d(BasicLocationDO entity) {
        if ( entity == null ) {
            return null;
        }

        BasicLocation basicLocation = new BasicLocation();

        basicLocation.setId( entity.getId() );
        basicLocation.setLocationCode( entity.getLocationCode() );
        basicLocation.setLocationNameCn( entity.getLocationNameCn() );
        basicLocation.setLocationNameEn( entity.getLocationNameEn() );
        basicLocation.setLocationTag( entity.getLocationTag() );

        return basicLocation;
    }

    @Override
    public List<BasicLocation> e2dList(List<BasicLocationDO> doList) {
        if ( doList == null ) {
            return null;
        }

        List<BasicLocation> list = new ArrayList<BasicLocation>( doList.size() );
        for ( BasicLocationDO basicLocationDO : doList ) {
            list.add( e2d( basicLocationDO ) );
        }

        return list;
    }
}
