package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.framework.cache.redis.entity.BasicValuesetOptionCache;
import com.dexpo.module.base.domain.model.agg.BasicValueSetOption;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.BasicValuesetOptionDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:30:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class BasicValueSetOptionConvertImpl implements BasicValueSetOptionConvert {

    @Override
    public BasicValuesetOptionDO d2e(BasicValueSetOption baseValueSetOption) {
        if ( baseValueSetOption == null ) {
            return null;
        }

        BasicValuesetOptionDO basicValuesetOptionDO = new BasicValuesetOptionDO();

        basicValuesetOptionDO.setId( baseValueSetOption.getId() );
        basicValuesetOptionDO.setValuesetCode( baseValueSetOption.getValuesetCode() );
        basicValuesetOptionDO.setOptionCode( baseValueSetOption.getOptionCode() );
        basicValuesetOptionDO.setOptionDescriptionCn( baseValueSetOption.getOptionDescriptionCn() );
        basicValuesetOptionDO.setOptionDescriptionEn( baseValueSetOption.getOptionDescriptionEn() );
        basicValuesetOptionDO.setOptionOrder( baseValueSetOption.getOptionOrder() );
        basicValuesetOptionDO.setParentCode( baseValueSetOption.getParentCode() );

        return basicValuesetOptionDO;
    }

    @Override
    public BasicValueSetOption e2d(BasicValuesetOptionDO entity) {
        if ( entity == null ) {
            return null;
        }

        BasicValueSetOption basicValueSetOption = new BasicValueSetOption();

        basicValueSetOption.setId( entity.getId() );
        basicValueSetOption.setValuesetCode( entity.getValuesetCode() );
        basicValueSetOption.setOptionCode( entity.getOptionCode() );
        basicValueSetOption.setOptionDescriptionCn( entity.getOptionDescriptionCn() );
        basicValueSetOption.setOptionDescriptionEn( entity.getOptionDescriptionEn() );
        basicValueSetOption.setOptionOrder( entity.getOptionOrder() );
        basicValueSetOption.setParentCode( entity.getParentCode() );

        return basicValueSetOption;
    }

    @Override
    public List<BasicValueSetOption> e2dList(List<BasicValuesetOptionDO> doList) {
        if ( doList == null ) {
            return null;
        }

        List<BasicValueSetOption> list = new ArrayList<BasicValueSetOption>( doList.size() );
        for ( BasicValuesetOptionDO basicValuesetOptionDO : doList ) {
            list.add( e2d( basicValuesetOptionDO ) );
        }

        return list;
    }

    @Override
    public BasicValuesetOptionCache toBasicValuesetOptionCache(BasicValuesetOptionDO optionDO) {
        if ( optionDO == null ) {
            return null;
        }

        BasicValuesetOptionCache basicValuesetOptionCache = new BasicValuesetOptionCache();

        basicValuesetOptionCache.setId( optionDO.getId() );
        basicValuesetOptionCache.setValuesetCode( optionDO.getValuesetCode() );
        basicValuesetOptionCache.setOptionCode( optionDO.getOptionCode() );
        basicValuesetOptionCache.setOptionDescriptionCn( optionDO.getOptionDescriptionCn() );
        basicValuesetOptionCache.setOptionDescriptionEn( optionDO.getOptionDescriptionEn() );
        basicValuesetOptionCache.setOptionOrder( optionDO.getOptionOrder() );
        basicValuesetOptionCache.setParentCode( optionDO.getParentCode() );

        return basicValuesetOptionCache;
    }

    @Override
    public List<BasicValuesetOptionCache> toBasicValuesetOptionCache(List<BasicValuesetOptionDO> optionDOs) {
        if ( optionDOs == null ) {
            return null;
        }

        List<BasicValuesetOptionCache> list = new ArrayList<BasicValuesetOptionCache>( optionDOs.size() );
        for ( BasicValuesetOptionDO basicValuesetOptionDO : optionDOs ) {
            list.add( toBasicValuesetOptionCache( basicValuesetOptionDO ) );
        }

        return list;
    }
}
