package com.dexpo.module.base.infrastructure.converter;

import com.dexpo.module.base.domain.model.agg.CommonTodo;
import com.dexpo.module.base.infrastructure.tunnel.dataobject.CommonTodoDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T10:30:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.7 (Microsoft)"
)
public class CommonTodoConvertImpl implements CommonTodoConvert {

    @Override
    public CommonTodoDO d2e(CommonTodo commonTodo) {
        if ( commonTodo == null ) {
            return null;
        }

        CommonTodoDO commonTodoDO = new CommonTodoDO();

        commonTodoDO.setCreateUserName( commonTodo.getCreateUserName() );
        commonTodoDO.setCreateUser( commonTodo.getCreateUser() );
        commonTodoDO.setUpdateUserName( commonTodo.getUpdateUserName() );
        commonTodoDO.setUpdateUser( commonTodo.getUpdateUser() );
        commonTodoDO.setId( commonTodo.getId() );
        commonTodoDO.setExhibitionId( commonTodo.getExhibitionId() );
        commonTodoDO.setTodoTitle( commonTodo.getTodoTitle() );
        commonTodoDO.setBusinessType( commonTodo.getBusinessType() );
        commonTodoDO.setBusinessNo( commonTodo.getBusinessNo() );
        commonTodoDO.setUserType( commonTodo.getUserType() );
        commonTodoDO.setUserId( commonTodo.getUserId() );
        commonTodoDO.setStatus( commonTodo.getStatus() );
        commonTodoDO.setGenerationTime( commonTodo.getGenerationTime() );
        commonTodoDO.setDoneTime( commonTodo.getDoneTime() );

        return commonTodoDO;
    }

    @Override
    public CommonTodo e2d(CommonTodoDO entity) {
        if ( entity == null ) {
            return null;
        }

        CommonTodo commonTodo = new CommonTodo();

        commonTodo.setId( entity.getId() );
        commonTodo.setExhibitionId( entity.getExhibitionId() );
        commonTodo.setTodoTitle( entity.getTodoTitle() );
        commonTodo.setBusinessType( entity.getBusinessType() );
        commonTodo.setBusinessNo( entity.getBusinessNo() );
        commonTodo.setUserType( entity.getUserType() );
        commonTodo.setUserId( entity.getUserId() );
        commonTodo.setStatus( entity.getStatus() );
        commonTodo.setGenerationTime( entity.getGenerationTime() );
        commonTodo.setDoneTime( entity.getDoneTime() );
        commonTodo.setCreateUserName( entity.getCreateUserName() );
        commonTodo.setCreateUser( entity.getCreateUser() );
        commonTodo.setUpdateUserName( entity.getUpdateUserName() );
        commonTodo.setUpdateUser( entity.getUpdateUser() );

        return commonTodo;
    }

    @Override
    public List<CommonTodo> e2dList(List<CommonTodoDO> doList) {
        if ( doList == null ) {
            return null;
        }

        List<CommonTodo> list = new ArrayList<CommonTodo>( doList.size() );
        for ( CommonTodoDO commonTodoDO : doList ) {
            list.add( e2d( commonTodoDO ) );
        }

        return list;
    }
}
