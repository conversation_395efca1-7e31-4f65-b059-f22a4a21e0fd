package com.dexpo.module.exhibition;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { MetricsAutoConfiguration.class, DataSourceAutoConfiguration.class })
@Slf4j
@EnableDiscoveryClient
@ComponentScan({"com.dexpo.framework.security","com.dexpo.module.exhibition","com.dexpo.framework.cache","com.dexpo.framework.web"})
@EnableFeignClients(value = {"com.dexpo.module.exhibition"})
public class ExhibitionServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(ExhibitionServerApplication.class, args);
        log.info("启动成功！");
    }

}
