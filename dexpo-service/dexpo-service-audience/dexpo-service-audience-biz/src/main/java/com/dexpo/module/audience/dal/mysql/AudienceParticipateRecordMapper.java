package com.dexpo.module.audience.dal.mysql;

import com.dexpo.framework.mybatis.core.mapper.BaseMapperX;
import com.dexpo.module.audience.dal.dataobject.AudienceParticipateRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 观众参展记录 Mapper
 */
@Mapper
public interface AudienceParticipateRecordMapper extends BaseMapperX<AudienceParticipateRecordDO> {

    /**
     * 激活
     *
     * @param ids
     */
    void activeByIds(@Param("ids") List<Long> ids);

    AudienceParticipateRecordDO getById(Long id);

}