package com.dexpo.module.audience.app.service.impl;

import com.dexpo.framework.cache.redis.entity.audience.AudienceBaseInfoCache;
import com.dexpo.framework.cache.redis.entity.exhibition.ExhibitionInfoCache;
import com.dexpo.framework.common.enums.LoginTypeEnum;
import com.dexpo.framework.common.enums.UserTypeEnum;
import com.dexpo.framework.common.exception.ServiceException;
import com.dexpo.framework.common.exception.enums.AudienceServiceErrorCodeEnum;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.module.audience.api.dto.audience.AudienceLoginDTO;
import com.dexpo.module.audience.api.vo.audience.AudienceLoginInfoVO;
import com.dexpo.module.audience.app.convert.AudienceBaseInfoConvert;
import com.dexpo.module.audience.app.convert.DataWarehouseAudienceSyncMessageConvert;
import com.dexpo.module.audience.app.service.AudienceMemberAppService;
import com.dexpo.module.audience.domain.model.AudienceBaseInfo;
import com.dexpo.module.audience.domain.model.AudienceParticipateRecord;
import com.dexpo.module.audience.domain.service.AudienceBaseInfoDomainService;
import com.dexpo.module.audience.domain.service.AudienceParticipateRecordDomainService;
import com.dexpo.module.audience.domain.service.AudienceSignRecordDomainService;
import com.dexpo.module.audience.infrastructure.integration.base.CacheOptService;
import com.dexpo.module.audience.infrastructure.mq.producer.DataWarehouseChannelProducer;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceSyncMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: Maojie
 * @CreateTime: 2025-06-21
 * @Description:
 */

@Service
@Slf4j
public class AudienceMemberAppServiceImpl implements AudienceMemberAppService {

    @Resource
    private CacheOptService cacheOptService;

    @Resource
    private AudienceBaseInfoDomainService audienceBaseInfoDomainService;

    @Resource
    private AudienceParticipateRecordDomainService audienceParticipateRecordDomainService;

    @Resource
    private AudienceSignRecordDomainService audienceSignRecordDomainService;

    @Resource
    private DataWarehouseChannelProducer dataWarehouseChannelProducer;


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public AudienceLoginInfoVO audienceLogin(AudienceLoginDTO loginDTO) {
        checkValidCode(loginDTO);
        String loginTool = loginDTO.getLoginTool();
        Long exhibitionId = loginDTO.getExhibitionId();
        AudienceLoginInfoVO audienceLoginInfoVO = new AudienceLoginInfoVO();
        AudienceBaseInfo baseInfoDO = AudienceBaseInfo.checkLoginToolAndInitAudienceBaseInfo(loginTool);
        if(StringUtils.isBlank(baseInfoDO.getAudienceMobile())){
            audienceLoginInfoVO.setLoginType(LoginTypeEnum.PHONE.getCode());
        }else {
            audienceLoginInfoVO.setLoginType(LoginTypeEnum.EMAIL.getCode());
        }
        Long id;
        AudienceBaseInfo oldBaseInfoDO = getOldAudienceBaseInfoDO(loginTool);
        boolean isNewMember;
        if(oldBaseInfoDO == null){//新用户
            /**
             * 这里catch DuplicateKeyException是因为有可能极端情形下会
             * 出现在redis没有而在数据量有数据的状况,这里如果触发了手机号或邮箱的唯一
             * 索引异常则说明该数据已经在数据库存在了 需要查询出这个数据并set到redis中
             */
            try {
                baseInfoDO =audienceBaseInfoDomainService.save(baseInfoDO);
                isNewMember = Boolean.TRUE;
            }catch (DuplicateKeyException e){
                log.info("mediaLogin duplicate key"+e);
                isNewMember = Boolean.FALSE;
                baseInfoDO = audienceBaseInfoDomainService.getAudienceBaseInfoByMobileOrEmail(loginTool);
            }
            id = baseInfoDO.getId();
            //将新用户的用户信息写到cache中
            cacheOptService.setAudienceBaseInfoCache(AudienceBaseInfoConvert.INSTANCE.entityToCache(baseInfoDO),loginTool);
        }else{//老用户
            isNewMember = Boolean.FALSE;
            id = oldBaseInfoDO.getId();
        }
        AudienceBaseInfo baseInfoValue = oldBaseInfoDO == null ? baseInfoDO : oldBaseInfoDO;
        //参会记录
        AudienceParticipateRecord audienceParticipateRecord = audienceParticipateRecordDomainService.saveIfNotExist(id, exhibitionId);
        //协议记录
        audienceSignRecordDomainService.saveIfNotExist(id, loginDTO.getAgreementId());
        // 生成token 缓存token和用户信息
        String token = getToken(id, baseInfoValue);
        audienceLoginInfoVO.setNewMember(isNewMember);
        audienceLoginInfoVO.setToken(token);
        audienceLoginInfoVO.setAudienceBaseInfoVO(AudienceBaseInfoConvert.INSTANCE.toVo(baseInfoValue));
        audienceLoginInfoVO.setLoginTool(loginDTO.getLoginTool());
        //todo 同步数据给数仓
        if(isNewMember){//新用户需要同步给数仓
            ExhibitionInfoCache exhibitionInfoCache = cacheOptService.getExhibitionInfoCacheById(exhibitionId);
            syncDataToWarehouse(audienceParticipateRecord,baseInfoValue,exhibitionInfoCache);
        }
        return audienceLoginInfoVO;
    }


    private void syncDataToWarehouse(AudienceParticipateRecord audienceParticipateRecord, AudienceBaseInfo baseInfo, ExhibitionInfoCache exhibitionInfoCache){
        DataWarehouseAudienceSyncMessage syncMessage = DataWarehouseAudienceSyncMessageConvert.INSTANCE.convert(audienceParticipateRecord,baseInfo,exhibitionInfoCache);
        dataWarehouseChannelProducer.dataWarehouseAudienceSyncChannel(syncMessage);
    }


    private String getToken(Long id, AudienceBaseInfo baseInfoDO) {
        LoginUser loginUser = new LoginUser();
        loginUser.setId(id);
        loginUser.setUserType(UserTypeEnum.AUDIENCE.getValue());
        loginUser.setMemberCode(baseInfoDO.getAudienceCode());
        /**
         * 因为媒体注册的姓名在审核之前不是有效的(可能会变化),所以这里不放memberName到token中,
         * 防止因为媒体注册的姓名被修改,导致token中的memberName和数据库中的memberName不一致,以及冗余存储的memberName出现错误
         */
        loginUser.setMemberMobile(baseInfoDO.getAudienceMobile());
        loginUser.setMemberEmail(baseInfoDO.getAudienceEmail());
        return cacheOptService.getToken(loginUser);
    }

    private AudienceBaseInfo getOldAudienceBaseInfoDO(String loginTool){
        AudienceBaseInfoCache oldBaseInfoCache = cacheOptService.getAudienceBaseInfoCache(loginTool);
        return AudienceBaseInfoConvert.INSTANCE.cacheToEntity(oldBaseInfoCache);
    }




    private void checkValidCode(AudienceLoginDTO loginDTO) {
        boolean validBoolean = cacheOptService.validCode(loginDTO.getLoginTool(), loginDTO.getValidCode());
        if (!validBoolean) {// 校验失败
            throw new ServiceException(AudienceServiceErrorCodeEnum.AUDIENCE_LOGIN_VALID_ERROR);
        }
    }
}
