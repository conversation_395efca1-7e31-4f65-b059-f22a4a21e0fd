package com.dexpo.module.integration.wechatpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@ApiModel("支付回调请求DTO")
@Data
public class PaymentCallbackRequestDTO {

    @ApiModelProperty("回调请求id")
    String callBackRequestId;

    @ApiModelProperty("渠道编号")
    String channelCode;

    @ApiModelProperty("回调请求编号")
    String callBackRequestCode;

    @ApiModelProperty("状态编号")
    String statusCode;

    @ApiModelProperty("体")
    String body;

    @ApiModelProperty("头")
    String head;

    @ApiModelProperty("支付单编号")
    String paymentBillNo;

    @ApiModelProperty("支付单id")
    String paymentBillId;

    @ApiModelProperty("操作人")
    String operator;

    @ApiModelProperty("是否成功")
    boolean success;

    @ApiModelProperty("参数")
    Map<String, Object> parms;
}
