package com.dexpo.module.integration.config;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


/**
 * 数仓接口配置类
 */
@Configuration
@Data
public class DataWareHouseApiConfig  {


    @Value("${data_warehouse.client_id}")
    private String clientId;

    @Value("${data_warehouse.grant_type}")
    private String grantType;
    

    @Value("${data_warehouse.client_secret}")
    private String clientSecret;

    @Value("${data_warehouse.token_url}")
    private String tokenUrl;

    @Value("${data_warehouse.hostname}")
    private String hostname;

    @Value("${data_warehouse.environment}")
    private String environment;

}
