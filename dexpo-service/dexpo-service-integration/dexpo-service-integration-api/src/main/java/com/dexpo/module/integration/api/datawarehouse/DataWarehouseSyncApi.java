package com.dexpo.module.integration.api.datawarehouse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.module.integration.api.datawarehouse.message.DataWarehouseAudienceDexpoDTO;
import com.dexpo.module.integration.enums.ApiConstants;

/**
 * 数据仓库往中台同步数据接口
 */
@FeignClient(name = ApiConstants.NAME)
public interface DataWarehouseSyncApi {

    /**
     * 观众注册信息同步接口
     */
    @PostMapping("/api/data-warehouse/sync/audience")
    CommonResult<Boolean> syncDataWarehouseSyncAudience(@RequestBody DataWarehouseAudienceDexpoDTO dto);

}
