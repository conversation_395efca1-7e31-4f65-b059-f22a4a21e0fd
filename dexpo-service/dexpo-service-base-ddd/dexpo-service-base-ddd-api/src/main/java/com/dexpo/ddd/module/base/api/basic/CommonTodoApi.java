package com.dexpo.ddd.module.base.api.basic;

import com.dexpo.ddd.module.base.api.basic.dto.CommenTodoPageQueryDTO;
import com.dexpo.ddd.module.base.api.basic.vo.CommonTodoVO;
import com.dexpo.ddd.module.base.enums.ApiConstants;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = ApiConstants.NAME)
public interface CommonTodoApi {

    String PREFIX = "/common/todo";

    /**
     * 分页获取待办事项
     */
    @PostMapping(PREFIX + "/page")
    CommonResult<PageResult<CommonTodoVO>> getPage(@Valid @RequestBody CommenTodoPageQueryDTO req);
}
