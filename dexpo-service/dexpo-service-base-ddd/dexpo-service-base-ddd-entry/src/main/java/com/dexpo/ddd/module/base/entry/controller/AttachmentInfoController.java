package com.dexpo.ddd.module.base.entry.controller;

import com.dexpo.ddd.module.base.api.attachment.AttachmentApi;
import com.dexpo.ddd.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.ddd.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.ddd.module.base.app.api.AttachmentInfoAppService;
import com.dexpo.framework.common.pojo.CommonResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * attachment controller
 *
 * <AUTHOR> Xiaohua 18/06/2025 16:05
 **/
@RestController
@Validated
@Slf4j
public class AttachmentInfoController implements AttachmentApi {

    @Resource
    private AttachmentInfoAppService attachmentInfoAppService;

    @Override
    public CommonResult<AttachmentInfoVO> findFileById(Long id) {
        AttachmentInfoVO attachmentInfoVO = attachmentInfoAppService.findById(id);
        return CommonResult.success(attachmentInfoVO);
    }

    @Override
    public CommonResult<AttachmentInfoVO> createAttachment(AttachmentInfoDTO attachmentInfoDTO) {
        attachmentInfoAppService.createAttachmentInfo(attachmentInfoDTO);
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<AttachmentInfoVO>> findFileByIds(List<Long> idList) {
        return null;
    }

    @Override
    public CommonResult<AttachmentInfoVO> findFileByBusinessType(String businessType) {
        return null;
    }
}
