<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dexpo</groupId>
    <artifactId>dexpo-dependencies</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>dexpo-dependencies</name>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>
    <url>doc</url>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <!-- 统一依赖管理 -->
        <spring.boot.version>3.4.4</spring.boot.version>
        <spring.cloud.version>2024.0.1</spring.cloud.version>
        <spring.cloud.alibaba.version>2023.0.3.2</spring.cloud.alibaba.version>
        <!-- Web 相关 -->
        <servlet.versoin>2.5</servlet.versoin>
        <swagger.version>2.2.8</swagger.version>
        <springdoc.version>2.8.3</springdoc.version>
        <knife4j.version>4.6.0</knife4j.version>
        <!-- DB 相关 -->
        <druid.version>1.2.24</druid.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <mybatis-plus-jsqlparser.version>3.5.9</mybatis-plus-jsqlparser.version>
        <mybatis-plus-generator.version>3.5.9</mybatis-plus-generator.version>
        <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
        <mybatis-plus-join-boot-starter.version>1.4.5</mybatis-plus-join-boot-starter.version>
        <redisson.version>3.23.4</redisson.version>
        <kryo.version>5.5.0</kryo.version>
        <dm8.jdbc.version>8.1.2.141</dm8.jdbc.version>
        <pagehelper.version>2.1.0</pagehelper.version>
        <!-- RPC 相关 -->
        <!-- Config 配置中心相关 -->
        <apollo.version>1.9.2</apollo.version>
        <!-- Job 定时任务相关 -->
        <xxl-job.version>2.3.1</xxl-job.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.3</lock4j.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>8.0.2.RELEASE</podam.version>
        <jedis-mock.version>1.0.7</jedis-mock.version>
        <mockito-inline.version>4.11.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>6.8.0</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.5</captcha-plus.version>
        <jsoup.version>1.15.4</jsoup.version>
        <lombok.version>1.18.36</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.37</hutool.version>
        <easyexcel.verion>4.0.3</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>2.0.57</fastjson.version>
        <guava.version>33.4.8-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <commons-net.version>3.9.0</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.7.0</tika-core.version>
        <netty-all.version>4.1.90.Final</netty-all.version>
        <ip2region.version>2.7.0</ip2region.version>
        <reflections.version>0.10.2</reflections.version>
        <!-- 三方云服务相关 -->
        <okio.version>3.0.0</okio.version>
        <okhttp3.version>4.10.0</okhttp3.version>
        <commons-io.version>2.18.0</commons-io.version>
        <minio.version>8.5.4</minio.version>
        <aliyun-java-sdk-core.version>4.6.3</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-dysmsapi.version>2.2.1</aliyun-java-sdk-dysmsapi.version>
        <tencentcloud-sdk-java.version>3.1.758</tencentcloud-sdk-java.version>
        <justauth.version>1.0.1</justauth.version>
        <jimureport.version>1.5.8</jimureport.version>
        <xercesImpl.version>2.12.2</xercesImpl.version>
        <weixin-java.version>4.5.0</weixin-java.version>
        <spring-kafka.version>3.1.1</spring-kafka.version>
        <awssdk.version>2.15.0</awssdk.version>
        <commons-beanutils.version>1.10.1</commons-beanutils.version>
        <tk.mybatis.mapper.version>5.0.1</tk.mybatis.mapper.version>
        <tk.mybatis.mapper-spring-boot-starter.version>5.0.1</tk.mybatis.mapper-spring-boot-starter.version>
        <cglib.version>3.3.0</cglib.version>
        <zxing.version>3.5.3</zxing.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <shedlock.version>6.3.0</shedlock.version>
        <shedlock-provider-redis-jedis.version>4.48.0</shedlock-provider-redis-jedis.version>
        <jjwt.version>0.12.6</jjwt.version>
        <jpush-client.version>3.7.7</jpush-client.version>
        <mysql-connector-j.version>9.2.0</mysql-connector-j.version>
        <springfox-swagger2.version>3.0.0</springfox-swagger2.version>
        <springfox-swagger-ui.version>3.0.0</springfox-swagger-ui.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring 核心 -->
            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-integration-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-base-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-member-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-exhibition-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-log-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-service-authorize-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 业务组件 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-social</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-ip</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-captcha</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-desensitize</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

              <!-- RPC 远程调用相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-kubernetes</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-rpc</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-models</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-common</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-webflux-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xingfudeshi</groupId> <!-- 接口文档 UI：knife4j -->
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- DB 相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-mybatis</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus-jsqlparser.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>


            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>

            <!-- Job 定时任务相关 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-job</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!-- 消息队列相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-mq</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- 服务保障相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-protection</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot2</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- 监控相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-monitor</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Test 测试相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-test</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
                <artifactId>jedis-mock</artifactId>
                <version>${jedis-mock.version}</version>
            </dependency>

            <dependency>
                <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
                <artifactId>podam</artifactId>
                <version>${podam.version}</version>
            </dependency>




            <!-- 工具类相关 -->
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-cache</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-security</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>


            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-web</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-biz-data-permission-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-biz-data-permission</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-biz-operatelog</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

             <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-excel</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.smallbun.screw</groupId>
                <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
                <version>${screw.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.freemarker</groupId>
                        <artifactId>freemarker</artifactId> <!-- 移除 Freemarker 依赖，采用 Velocity 作为模板引擎 -->
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId> <!-- 最新版screw-core1.0.5依赖fastjson1.2.73存在漏洞，移除。 -->
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>

            <!-- 三方云服务相关 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
             <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-file</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.dexpo</groupId>
                <artifactId>dexpo-framework-starter-message</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-mp-spring-boot-starter</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>

            <!-- SMS SDK begin -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>opentracing-api</artifactId>
                        <groupId>io.opentracing</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>opentracing-util</artifactId>
                        <groupId>io.opentracing</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun-java-sdk-dysmsapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencentcloud-sdk-java.version}</version>
            </dependency>
            <!-- SMS SDK end -->

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
                <version>${justauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 积木报表-->
            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-spring-boot-starter</artifactId>
                <version>${jimureport.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>druid</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xercesImpl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${awssdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
