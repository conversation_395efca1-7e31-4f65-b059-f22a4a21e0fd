--- #################### 数据库相关配置 ####################
spring:
  cloud:
    function:
      # 消费者Bean方法命名
      definition: smsChannel,emailChannel
    stream:
      rocketmq:
        binder:
          name-server: rmq-cn-btz49fcqh07.cn-shanghai.rmq.aliyuncs.com:8080
          access-key: ${ACCESS_KEY}
          secret-key: ${SECRET_KEY}
      bindings:
        # 生产者
        smsChannel-out-0:
          binder: rocketmq
          destination: SMS-CHANNEL
        # 生产者
        emailChannel-out-0:
          binder: rocketmq
          destination: MAIL-CHANNEL
  data:
    redis:
      host: ${REDIS_HOST}
      port: 6379
      ssl:
        enabled: false
      password: ${REDIS_PASSWORD}
      database: 0

# 日志文件配置
logging:
  level:
    com.dexpo: debug
    # 配置自己写的 MyBatis Mapper 打印日志
    #com.dexpo.module.base.dal.mysql: debug

--- #################### RR低代码相关配置 ####################

# RR低代码配置项，设置当前项目所有自定义的配置
dexpo:
  info:
    base-package: com.dexpo # 基础包路径
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  security:
    mock-enable: true
  web:
    admin-ui:
      url: http://localhost:48080 # Admin UI 地址
      title: DEXPO Admin # Admin UI 标题
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  access-log: # 访问日志的配置项
    enable: false
  error-code: # 错误码相关配置项
    enable: false
  demo: false # 关闭演示模式

#################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html