package com.dexpo.module.bff.manage.controller.attachment;

import com.dexpo.framework.common.exception.enums.ErrorCodeEnums;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.util.servlet.ServletUtils;
import com.dexpo.framework.security.core.LoginUser;
import com.dexpo.framework.security.core.util.SecurityFrameworkUtils;
import com.dexpo.module.base.api.attachment.AttachmentApi;
import com.dexpo.module.base.api.attachment.dto.AttachmentInfoDTO;
import com.dexpo.module.base.api.attachment.vo.AttachmentInfoVO;
import com.dexpo.module.integration.api.flie.FileApi;
import com.dexpo.module.integration.api.flie.vo.FileVo;
import com.dexpo.module.integration.utils.FileUtils;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@Validated
public class AttachmentController {

    @Resource
    private AttachmentApi attachmentApi;

    @Resource
    private FileApi fileApi;

    @PostMapping("/file/findFileById")
    @Operation(summary = "根据路径获取文件信息")
    public CommonResult<AttachmentInfoVO> findFileByPath(@RequestBody AttachmentInfoDTO attachmentInfoDTO) {
        return attachmentApi.findFileById(attachmentInfoDTO.getId());
    }

    @PostMapping("/file/findFileByIds")
    @Operation(summary = "根据文件id集合查询文件信息")
    public CommonResult<List<AttachmentInfoVO>> findFileByIds(@RequestBody AttachmentInfoDTO attachmentInfoDTO) {
        return attachmentApi.findFileByIds(attachmentInfoDTO.getIdList());
    }


    @PostMapping("/file/uploadFile")
    @Operation(summary = "上传文件")
    public CommonResult<AttachmentInfoVO> uploadFile(@RequestParam("file") MultipartFile file) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        String originalFilename = file.getOriginalFilename();
        // 上传文件
        String path = FileUtils.getFilePath(loginUser.getMemberCode());
        CommonResult<String> result = null;
        try {
            result = fileApi.uploadFileByBytes(originalFilename, path, file.getBytes());
        } catch (IOException e) {
            throw ErrorCodeEnums.BFF_USER_FILE_UPLOAD_FAIL.getServiceException();
        }

        String fileUrl = result.getData();
        AttachmentInfoDTO attachmentInfoDTO = new AttachmentInfoDTO();
        attachmentInfoDTO.setAttachmentName(originalFilename);
        attachmentInfoDTO.setAttachmentPath(fileUrl);
        attachmentInfoDTO.setAttachmentSize(file.getSize());
        if (originalFilename != null) {
            String fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            attachmentInfoDTO.setAttachmentType(fileType);
        }

        // 添加附件记录
        return attachmentApi.createAttachment(attachmentInfoDTO);
    }

    private static void extracted(HttpServletResponse response, String path) {
        log.warn("[getFileContent] path({}) 文件不存在]", path);
        response.setStatus(HttpStatus.NOT_FOUND.value());
    }

    @PostMapping("/file/templateDownload")
    @Operation(summary = "下载文件")
    public void templateDownload(@RequestParam("businessType") String businessType, HttpServletResponse response) throws Exception {
        CommonResult<AttachmentInfoVO> businessTypeFile = attachmentApi.findFileByBusinessType(businessType);
        String path = businessTypeFile.getData().getAttachmentPath();
        CommonResult<FileVo> result = fileApi.getFileContent(path);
        FileVo fileVo = result.getData();
        if (fileVo.getFileBytes() == null) {
            extracted(response, path);
            return;
        }
        response.addHeader("Content-Type", fileVo.getFileType());
        ServletUtils.writeAttachment(response, path, fileVo.getFileBytes());
    }

    @PostMapping("/file/downloadFile")
    @Operation(summary = "下载文件")
    public void downloadFile(@RequestParam("path") String path, HttpServletResponse response) throws Exception {
        CommonResult<FileVo> result = fileApi.getFileContent(path);
        FileVo fileVo = result.getData();
        if (fileVo.getFileBytes() == null) {
            extracted(response, path);
            return;
        }
        response.addHeader("Content-Type", fileVo.getFileType());
        ServletUtils.writeAttachment(response, path, fileVo.getFileBytes());
    }

    @GetMapping("/file/preview")
    @Operation(summary = "预览文件")
    public void preview(@RequestParam("id") Long id, HttpServletResponse response) throws Exception {
        CommonResult<AttachmentInfoVO> fileById = attachmentApi.findFileById(id);
        String path = fileById.getData().getAttachmentPath();
        CommonResult<FileVo> result = fileApi.getFileContent(path);
        FileVo fileVo = result.getData();
        if (fileVo.getFileBytes() == null) {
            extracted(response, path);
            return;
        }
        ServletUtils.writeAttachmentPreview(response, fileVo.getFileType(), fileById.getData().getAttachmentName(), fileVo.getFileBytes());
    }
}

