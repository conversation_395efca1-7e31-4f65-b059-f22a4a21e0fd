package com.dexpo.module.bff.manage;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication(exclude = {MetricsAutoConfiguration.class, DataSourceAutoConfiguration.class})
@Slf4j
@EnableDiscoveryClient
@ComponentScan({"com.dexpo.framework.security", "com.dexpo.module.bff.manage",
        "com.dexpo.framework.cache","com.dexpo.framework.web"})
@EnableFeignClients(value = {"com.dexpo.module.base.api"
        , "com.dexpo.module.integration.api", "com.dexpo.module.member", "com.dexpo.module.exhibition",
        "com.dexpo.module.audience"})
public class DexpoServiceBFFManageApplication {

    public static void main(String[] args) {
        SpringApplication.run(DexpoServiceBFFManageApplication.class, args);
        log.info("启动成功！");
    }
}
