-- 将该建表 SQL 语句，添加到 dtt-module-${table.moduleName}-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "${table.tableName.toLowerCase()}" (
#foreach ($column in $columns)
#if (${column.javaType} == 'Long')
    #set ($dataType='bigint')
#elseif (${column.javaType} == 'Integer')
    #set ($dataType='int')
#elseif (${column.javaType} == 'Boolean')
    #set ($dataType='bit')
#elseif (${column.javaType} == 'Date')
    #set ($dataType='datetime')
#else
    #set ($dataType='varchar')
#end
    #if (${column.primaryKey})##处理主键
    "${column.javaField}"#if (${column.javaType} == 'String') ${dataType} NOT NULL#else ${dataType} NOT NULL GENERATED BY DEFAULT AS IDENTITY#end,
    #else
    #if (${column.columnName} == 'create_time')
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    #elseif (${column.columnName} == 'update_time')
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    #elseif (${column.columnName} == 'creator' || ${column.columnName} == 'updater')
    "${column.columnName}" ${dataType} DEFAULT '',
    #elseif (${column.columnName} == 'deleted')
    "deleted" bit NOT NULL DEFAULT FALSE,
    #else
    "${column.columnName.toLowerCase()}" ${dataType}#if (${column.nullable} == false) NOT NULL#end,
    #end
    #end
#end
    PRIMARY KEY ("${primaryColumn.columnName.toLowerCase()}")
) COMMENT '${table.tableComment}';

-- 将该删表 SQL 语句，添加到 dtt-module-${table.moduleName}-biz 模块的 test/resources/sql/clean.sql 文件里
DELETE FROM "${table.tableName}";
