package com.dexpo.system.controller.admin.sms;

import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.common.util.servlet.ServletUtils;
import com.dexpo.framework.operatelog.core.annotations.OperateLogAnnotation;
import com.dexpo.system.framework.sms.core.enums.SmsChannelEnum;
import com.dexpo.system.service.sms.SmsSendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;

import static com.dexpo.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信回调")
@RestController
@RequestMapping("/system/sms/callback")
public class SmsCallbackController {

    @Resource
    private SmsSendService smsSendService;

    @PostMapping("/aliyun")
    @PermitAll
    @Operation(summary = "阿里云短信的回调", description = "参见 https://help.aliyun.com/document_detail/120998.html 文档")
    @OperateLogAnnotation(enable = false)
    public CommonResult<Boolean> receiveAliyunSmsStatus(HttpServletRequest request) throws Throwable {
        String text = ServletUtils.getBody(request);
        smsSendService.receiveSmsStatus(SmsChannelEnum.ALIYUN.getCode(), text);
        return success(true);
    }

    @PostMapping("/tencent")
    @PermitAll
    @Operation(summary = "腾讯云短信的回调", description = "参见 https://cloud.tencent.com/document/product/382/52077 文档")
    @OperateLogAnnotation(enable = false)
    public CommonResult<Boolean> receiveTencentSmsStatus(HttpServletRequest request) {
        //String text = ServletUtils.getBody(request)
        //smsSendService.receiveSmsStatus(SmsChannelEnum.TENCENT.getCode(), text)
        return success(true);
    }

}
