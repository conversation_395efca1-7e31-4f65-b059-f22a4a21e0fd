package com.dexpo.system.controller.admin.user;

import com.dexpo.system.controller.admin.user.vo.profile.UserProfileRespVO;
import com.dexpo.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import com.dexpo.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import com.dexpo.framework.common.enums.CommonStatusEnum;
import com.dexpo.framework.common.enums.UserTypeEnum;
import com.dexpo.framework.common.pojo.CommonResult;
import com.dexpo.framework.datapermission.core.annotation.DataPermission;
import com.dexpo.system.convert.business.BusinessConvert;
import com.dexpo.system.convert.user.UserConvert;
import com.dexpo.system.dal.dataobject.business.BusinessDO;
import com.dexpo.system.dal.dataobject.permission.RoleDO;
import com.dexpo.system.dal.dataobject.social.SocialUserDO;
import com.dexpo.system.dal.dataobject.user.AdminUserDO;
import com.dexpo.system.framework.security.config.PwdProperties;
import com.dexpo.system.service.business.BusinessService;
import com.dexpo.system.service.dept.DeptService;
import com.dexpo.system.service.dept.PostService;
import com.dexpo.system.service.permission.PermissionService;
import com.dexpo.system.service.permission.RoleService;
import com.dexpo.system.service.social.SocialUserService;
import com.dexpo.system.service.user.AdminUserService;
import com.dexpo.system.util.collection.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.dexpo.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.dexpo.framework.common.pojo.CommonResult.success;
import static com.dexpo.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.dexpo.system.enums.ErrorCodeConstants.FILE_IS_EMPTY;


@Tag(name = "管理后台 - 用户个人中心")
@RestController
@RequestMapping("/system/user/profile")
@Validated
@Slf4j
public class UserProfileController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private SocialUserService socialService;
    @Resource
    private BusinessService businessService;
    @Resource
    private PwdProperties pwdProperties;

    @GetMapping("/get")
    @Operation(summary = "获得登录用户信息")
    @DataPermission(enable = false) // 关闭数据权限，避免只查看自己时，查询不到部门。
    public CommonResult<UserProfileRespVO> profile() {
        // 获得用户基本信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        UserProfileRespVO resp = UserConvert.INSTANCE.convert03(user);
        // 获得用户角色
        List<RoleDO> userRoles = roleService.getRoleListFromCache(permissionService.getUserRoleIdListByUserId(user.getId()));
        userRoles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus()));
        resp.setRoles(UserConvert.INSTANCE.convertList(userRoles));
        // 默认角色或上次登录角色
        RoleDO usedRole = UserUtil.getUsedRole(user, userRoles);
        resp.setLastLoginRole(usedRole.getRoleCode());
        //获取当前角色下的业务线信息
        List<BusinessDO> businesses = businessService.getBusinessListWithRoles(userRoles);
        // List<BusinessDO> businesses = businessService.getBusinessList(convertSet(businessSet,Long::parseLong))
        resp.setBusinesses(BusinessConvert.INSTANCE.convertList03(businesses));
        //密码过期时间
        resp.setPwdExpireTime(user.getPwdLastModifyTime().plusDays(pwdProperties.getMaxValidDay()));

        // 获得社交用户信息
        List<SocialUserDO> socialUsers = socialService.getSocialUserList(user.getId(), UserTypeEnum.ADMIN.getValue());
        resp.setSocialUsers(UserConvert.INSTANCE.convertList03(socialUsers));
        return success(resp);
    }
    @PutMapping("/update")
    @Operation(summary = "修改用户个人信息")
    public CommonResult<Boolean> updateUserProfile(@Valid @RequestBody UserProfileUpdateReqVO reqVO) {
        userService.updateUserProfile(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-password")
    @Operation(summary = "修改用户个人密码")
    public CommonResult<Boolean> updateUserProfilePassword(@Valid @RequestBody UserProfileUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/switch-role")
    @Operation(summary = "切换用户角色")
    public CommonResult<Boolean> switchRole(@RequestParam("roleCode") String roleCode) {
        userService.switchRole(getLoginUserId(), roleCode);
        return success(true);
    }

    @RequestMapping(value = "/update-avatar", method = {RequestMethod.POST, RequestMethod.PUT}) // 解决 uni-app 不支持 Put 上传文件的问题
    @Operation(summary = "上传用户个人头像")
    public CommonResult<String> updateUserAvatar(@RequestParam("avatarFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        String avatar = userService.updateUserAvatar(getLoginUserId(), file.getInputStream());
        return success(avatar);
    }

}
