package com.dexpo.system.demo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.redisson.Redisson;
import org.redisson.api.*;
import org.redisson.config.Config;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;


class RedissonJobTest {
    @Test
    void test() throws ExecutionException, InterruptedException {

        Config config = new Config();
        config.useSingleServer().setPassword("PASSWORD").setAddress("redis://127.0.0.1:6379");
        RedissonClient redisson = Redisson.create(config);


        RBlockingDeque<String> blockingFairQueue = redisson.getBlockingDeque("queueName");

        RDelayedQueue<String> delayedQueue = redisson.getDelayedQueue(blockingFairQueue);

        long starTime = System.currentTimeMillis();

        // 取出并执行延迟任务
        takeAndHandleJOb(blockingFairQueue, delayedQueue, starTime);

        CompletableFuture<Void> addFuture = null;
        for (int i = 0; i < 100 * 100; i++) {
            int finalI = i;
            addFuture = CompletableFuture.runAsync(() -> {


                // 设置10秒后的2个任务
                delayedQueue.offer("product:up:" + finalI, 10, TimeUnit.SECONDS);
                delayedQueue.offer("order:cancel:" + finalI, 20, TimeUnit.SECONDS);
                delayedQueue.offer("order:cancel:" + finalI, 20, TimeUnit.SECONDS);
                //

            });

        }
        addFuture.get();
        long addOverTime = System.currentTimeMillis();
        System.out.println("存放队列耗时：" + (addOverTime - starTime) + "毫秒");
        try {
            Thread.sleep(2000);   //2428毫秒
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        CompletableFuture<Void> updateFuture = null;
        for (int i = 0; i < 100 * 100; i++) {
            int finalI = i;
            updateFuture = CompletableFuture.runAsync(() -> {
                int id = finalI;
                // product up任务设置为现在开始设置5秒后
                // 需要先取消
                delayedQueue.remove("product:up:" + id);

                // 重新设置延迟时间
                delayedQueue.offer("product:up:" + id, 5, TimeUnit.SECONDS);

            });
        }
        Assertions.assertNotNull(updateFuture);
        updateFuture.get();
        long changEndTime = System.currentTimeMillis();
        System.out.println("修改队列耗时：" + (changEndTime - addOverTime - 2000) + "毫秒");
        delayedQueue.destroy();

    }

    private static void takeAndHandleJOb(RBlockingQueue<String> blockingFairQueue, RDelayedQueue<String> delayedQueue, long starTime) {
        CompletableFuture.runAsync(() -> {
            try {
                while (true) {
                    String delayJob = blockingFairQueue.take();
                    CompletableFuture.runAsync(() -> {
                        // xxxx处理延时任务
                        try {
                            Thread.sleep(200);
                            System.out.println("处理" + delayJob + "的延迟任务");
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                        // 如果定时任务出错记录日志表 方便后续手动处理
                    });
                    if (delayedQueue.isEmpty() && blockingFairQueue.isEmpty()) {
                        long endTime = System.currentTimeMillis();
                        System.out.println("总执行耗时：" + (endTime - starTime) + "毫秒");
                        // 1762099毫秒
                        // 3105448毫秒  开发机器
                        // 1424892毫秒  本机二次
                        // 225604毫秒  本地三次
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
